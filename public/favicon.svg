<?xml version="1.0" encoding="UTF-8"?>
<svg id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 32 32">
  <!-- Generator: Adobe Illustrator 29.4.0, SVG Export Plug-In . SVG Version: 2.1.0 Build 152)  -->
  <defs>
    <style>
      .st0 {
        fill: #fff;
      }

      .st1 {
        fill: url(#Degradado_sin_nombre);
      }

      .st2 {
        fill: url(#Degradado_sin_nombre_4);
      }

      .st3 {
        fill: url(#Degradado_sin_nombre_3);
      }

      .st4 {
        fill: url(#Degradado_sin_nombre_2);
      }
    </style>
    <linearGradient id="Degradado_sin_nombre" data-name="Degradado sin nombre" x1="3" y1="16.09" x2="29" y2="16.09" gradientTransform="translate(0 34) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#9c27b0"/>
      <stop offset="1" stop-color="#e91e63"/>
    </linearGradient>
    <linearGradient id="Degradado_sin_nombre_2" data-name="Degradado sin nombre 2" x1="4.53" y1="24.93" x2="18.56" y2="24.93" gradientTransform="translate(0 34) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#9c27b0"/>
      <stop offset="1" stop-color="#e91e63"/>
    </linearGradient>
    <linearGradient id="Degradado_sin_nombre_3" data-name="Degradado sin nombre 3" x1="7.96" y1="7.36" x2="18.67" y2="7.36" gradientTransform="translate(0 34) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#9c27b0"/>
      <stop offset="1" stop-color="#e91e63"/>
    </linearGradient>
    <linearGradient id="Degradado_sin_nombre_4" data-name="Degradado sin nombre 4" x1="15.9" y1="28.68" x2="22.46" y2="28.68" gradientTransform="translate(0 34) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#9c27b0"/>
      <stop offset="1" stop-color="#e91e63"/>
    </linearGradient>
  </defs>
  <path class="st0" d="M29.42,15.75c-.29-2.61-1.35-5.12-2.9-6.89-.09-.1-.22-.17-.36-.17,0,0-.02,0-.03,0,.96-1.7.6-2.31.46-2.54-.58-.97-2.29-.5-3.67.02.02-.05.03-.1.04-.15.02-.18-.07-.36-.22-.46-1.9-1.24-4.25-1.93-6.83-1.98-.24,0-.45.17-.49.41,0,.04-.01.09-.01.14-.28-.23-.57-.46-.87-.69-1.18-.91-2.81-2.14-4.41-2.19-1.42-.03-2.16,1.27-2.81,2.42-.45.79-.91,1.61-1.48,1.77-.29.08-.62,0-1.04-.24-.15-.09-.33-.09-.48-.01-.15.08-.25.23-.27.4-.06.72.79,1.24,1.12,1.38.67.29,1.31.26,1.91.06-2.14,1.89-3.59,4.3-4.19,7.02-.94,4.2-.08,8.13,2.47,11.38,1.38,1.7,1.65,1.68,1.84,1.69.14,0,.28-.07.38-.18.48,1.58,2.58,2.41,3.96,2.96.2.08.39.15.56.23.22.09.48.01.61-.19.07-.1.09-.22.08-.33.25.2.54.37.85.5.68.26,1.37.32,2.03.18,0,.08.03.15.07.22.09.16.26.25.43.25.05,0,.11,0,.16-.03.16-.05.35-.05.56-.05.16,0,.32,0,.49-.01,3.56-.36,6.78-2.13,9.09-4.99,2.3-2.85,3.36-6.38,2.96-9.92ZM7.16,23.94h0s0,0,0,0Z"/>
  <g>
    <path class="st1" d="M24.48,24.01l.32-.69c.43-1.25.53-2.42.14-3.7-.16-.52-.49-.89-.62-1.39,1.27-2.23,1.3-4.92.41-7.3l.97-1.29.44-.45c1.57,1.78,2.52,4.26,2.78,6.61.78,7.08-4.55,13.64-11.61,14.36-.4.04-.78-.04-1.16.09.55-.5,1.13-.96,1.37-1.7-.98,1.09-2.27,1.65-3.72,1.09-1.93-.75-2.25-3.28-.24-4.13,1.82-.76,4.16.23,5.42,1.62.2.23.39.61.56.8q.06.07.17.05c.56-1.92,1.61-3.63,2.91-5.13-.45-.54-1.08-.87-1.76-1-1-.2-2.46.05-2.87-1.17-.27-.79-.12-1.58.19-2.33-.93.42-.82,1.73-1.09,2.59-.32,1.01-1.25,1.99-2.35,2.1-.09,0-.3.06-.28-.08,1.93-.66,2.58-2.38,2.25-4.32-.02-.1-.02-.38-.15-.37.11,2.15-.97,3.99-3.15,4.42-.83.16-1.7.1-2.54.21-2.01.27-4.12,1.36-3.69,3.72-.08.11-1.36-1.39-1.45-1.5-2.52-3.21-3.26-6.97-2.37-10.96.83-3.77,3.33-6.76,6.66-8.6-.02.29.37.71.21.97-.06.09-.55.4-.69.53-1.25,1.17-2.23,2.43-2.81,4.07-.55,1.55-.6,3.09-.52,4.71l-2.17.82c.78.33,1.61.2,2.43.28.38.96.71,1.95,1.36,2.77.03.18-.39.37-.52.51-.89.97-1.29,2.51-.72,3.73.04-1.27.35-2.5,1.39-3.31,1.66-1.29,3.25-1.54,4.51-3.58.07-.11.26-.35.17-.48-.65.76-1.97,2.13-2.91,2.43-.05.02-.14.02-.12-.04.38-.47.75-.93.97-1.5h-2.22c-.2,0-.82-.17-1.05-.24l-.56-.33c1.66-.52,3.18-1.35,4.57-2.4,2.72-2.06,4.91-5.41,8.07-6.74.07-.03.21-.18.27-.05-1.54,1.08-2.97,2.31-4.29,3.65l.12.04c2.18-1.79,4.64-3.56,7.27-4.63.67-.27,2.9-1.16,3.38-.36.51.86-1.07,3.04-1.63,3.73s-1.14,1.23-1.78,1.79c-.16.09-.33-.17-.45-.29-.54-.57-.97-1.34-1.58-1.86.66,1.96,2.8,3.19,2.23,5.51-.06.24-.34.96-.49,1.13-.22.24-.38-.24-.57-.32-.1-.04-.15.02-.12.12.95,1.46,2.72,2.69,2.9,4.55,0,.09.06.21-.07.19-.51-1.5-1.9-2.45-2.75-3.73-.19.1-.02.58.02.75.43,1.49,2.65,2.9,2.65,4.48v1.58l-.04-.03ZM16.71,13.23c.82-.85,1.59-1.6,2.15-2.67.06-.12.24-.36.16-.49-1.45,1.94-3.31,3.49-5.49,4.55l-2.8,1.04c2.94-.02,6.09-.84,8.05-3.16.11-.13.64-.83.69-.93.02-.04.02-.13-.04-.12-.76.73-1.7,1.47-2.71,1.78h-.01ZM21,16.07c-.01-.24-.21-.31-.13-.57.1-.31.54-.28.54-.52-.11-.28-.39-.42-.56-.66-.5-.74-.15-1.4-.17-2.17,0-.22-.1-.76-.16-.98-.03-.11-.01-.25-.16-.28.03.37-.42,1.04-.4,1.34.01.22.4.32.32.6l-.51-.23-.38.52.65.77c-.46.09-.76-.2-1.16-.32-.72.74-1.59,1.32-2.56,1.7.09.61.21,1.72.72,2.13.44.35,2.39.83,2.96.85.86.02.41-.67.53-1.07.05-.18.37-.26.31-.5l-.2-.12-.77-.08s1.13-.41,1.13-.41ZM7.84,22.98c2.53-2.46,7.71-.18,7.81-5.67l-.15-1.73c-.12-.02-.11.14-.14.23-.35,1.09-.47,2.3-1.09,3.28-.91,1.44-2.88,1.89-4.43,2.24l1.46-1.01c.94-.83,1.76-2.44,2-3.67.02-.08.08-.28-.06-.26-.81,1.23-1.74,2.54-2.83,3.52-1.34,1.21-3.11,2.02-3.39,4.05-.02.24.09.06.14-.02.22-.32.4-.69.69-.97h0ZM23.91,26.36c-.04-.98-.31-2.01-.97-2.75l.97,2.75Z"/>
    <path class="st4" d="M15.86,9.22c-1.46,1.29-2.73,2.95-4.2,4.26-1.61,1.45-3.58,2.7-5.79,2.91l2.66-1.5c.54-.39,2.27-1.92,2.42-2.52.27-1.11.26-3.82.18-5s-.44-2.19-1.5-2.78c-1.12.97-2.64,2.62-4.28,1.92-.33-.14-.85-.54-.82-.88,3.03,1.76,3.04-3.94,5.57-3.88,1.45.04,3.04,1.26,4.14,2.1,1.46,1.12,2.84,2.35,4.29,3.48.08.06-.04.12-.09.15-.41.27-.96.49-1.4.79-.41.28-.81.63-1.19.96h0Z"/>
    <path class="st3" d="M18.66,26.03c-1.7-1.57-5.5-2.29-7.02-.19-.95,1.31-.43,2.8.63,3.83-1.69-.71-4.97-1.7-4.19-4.15,1-3.15,8.16-1.87,10.14-.06.08.07.53.48.44.57Z"/>
    <path class="st2" d="M15.91,4.08c2.3.05,4.62.64,6.55,1.9-.42.18-1.08.65-1.54.58s-1.36-.67-1.9-.86c-1.37-.48-1.8-.32-2.98-1.39-.08-.07-.17-.07-.14-.23h0Z"/>
  </g>
</svg>