{"name": "new1", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "serve -s dist -l 3001"}, "dependencies": {"pocketbase": "^0.26.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.1", "serve": "^14.2.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.3.5", "vite": "^6.3.1"}}