import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { authService } from '../services/pocketbase';
import Layout from '../components/Layout';
import { SECURITY_CONFIG } from '../config';
import logger from '../services/logService';

// Función para sanitizar entradas de texto
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/`/g, '&#96;');
};

// Función para validar email
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const LoginPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [lockoutUntil, setLockoutUntil] = useState(null);

  // Verificar si el usuario ya está autenticado
  useEffect(() => {
    if (authService.isAuthenticated()) {
      // Redirigir a la página de administración si ya está autenticado
      navigate('/admin');
    }

    // Recuperar intentos de inicio de sesión y tiempo de bloqueo del almacenamiento local
    const storedAttempts = localStorage.getItem('loginAttempts');
    const storedLockout = localStorage.getItem('lockoutUntil');

    if (storedAttempts) {
      setLoginAttempts(parseInt(storedAttempts, 10));
    }

    if (storedLockout && new Date(storedLockout) > new Date()) {
      setLockoutUntil(new Date(storedLockout));
    } else if (storedLockout) {
      // Si el tiempo de bloqueo ha pasado, reiniciar los intentos
      localStorage.removeItem('lockoutUntil');
      localStorage.setItem('loginAttempts', '0');
      setLoginAttempts(0);
    }
  }, [navigate]);

  const handleLogin = async (e) => {
    e.preventDefault();

    // Verificar si el usuario está bloqueado
    if (lockoutUntil && new Date(lockoutUntil) > new Date()) {
      const timeLeft = Math.ceil((new Date(lockoutUntil) - new Date()) / 1000 / 60);
      setError(`Demasiados intentos fallidos. Por favor, inténtalo de nuevo en ${timeLeft} minutos.`);
      return;
    }

    // Validar email y contraseña
    if (!email.trim()) {
      setError('Por favor, ingresa tu correo electrónico.');
      return;
    }

    if (!validateEmail(email)) {
      setError('Por favor, ingresa un correo electrónico válido.');
      return;
    }

    if (!password) {
      setError('Por favor, ingresa tu contraseña.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Sanitizar email antes de enviarlo
      const sanitizedEmail = sanitizeInput(email.trim().toLowerCase());

      // Lógica de autenticación con PocketBase
      await authService.login(sanitizedEmail, password);

      // Reiniciar intentos de inicio de sesión
      localStorage.setItem('loginAttempts', '0');
      localStorage.removeItem('lockoutUntil');

      // Redirigir a la página de administración después del inicio de sesión exitoso
      // O a la página de donde vino el usuario si existe
      const from = location.state?.from?.pathname || '/admin';
      navigate(from);
    } catch (err) {
      logger.error('Error de inicio de sesión:', err);

      // Incrementar intentos fallidos
      const newAttempts = loginAttempts + 1;
      setLoginAttempts(newAttempts);
      localStorage.setItem('loginAttempts', newAttempts.toString());

      // Bloquear después de MAX_LOGIN_ATTEMPTS intentos fallidos
      if (newAttempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
        const lockoutTime = new Date(Date.now() + SECURITY_CONFIG.LOCKOUT_TIME);
        setLockoutUntil(lockoutTime);
        localStorage.setItem('lockoutUntil', lockoutTime.toISOString());
        const minutesLocked = Math.ceil(SECURITY_CONFIG.LOCKOUT_TIME / 60000);
        setError(`Demasiados intentos fallidos. Por favor, inténtalo de nuevo en ${minutesLocked} minutos.`);
      } else {
        setError('Credenciales inválidas. Por favor, inténtalo de nuevo.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="max-w-md mx-auto mt-10 bg-[#181828] p-8 rounded-lg shadow-lg border border-[#29293d]">
        <h1 className="text-2xl font-bold text-center text-[#e0b3ff] mb-6">Iniciar Sesión - Admin</h1>

        {error && (
          <div className="bg-red-900 bg-opacity-50 text-red-300 p-3 rounded-md mb-4 border border-red-800">
            {error}
          </div>
        )}

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
              Correo Electrónico
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-4 py-2 bg-[#23233a] border border-[#3d3d3d] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9c27b0] focus:border-transparent transition-colors"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
              Contraseña
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-4 py-2 bg-[#23233a] border border-[#3d3d3d] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9c27b0] focus:border-transparent transition-colors"
              placeholder="••••••••"
            />
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className={`w-full px-4 py-2 rounded-md text-white font-semibold transition-all duration-300 flex items-center justify-center gap-2 ${loading ? 'bg-gray-600 cursor-not-allowed' : 'bg-gradient-to-r from-[#9c27b0] to-[#e91e63] hover:opacity-90 shadow-md'}`}
            >
              {loading ? (
                <>
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Ingresando...</span>
                </>
              ) : (
                'Ingresar'
              )}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default LoginPage;