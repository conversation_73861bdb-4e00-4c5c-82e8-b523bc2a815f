import { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { modelosService } from '../services/pocketbase';
import { independientesService } from '../services/independientesService';
import { bannerService } from '../services/bannerService';
import { ofertasService } from '../services/ofertasService';
import { configService } from '../services/configService';
import AdminLayout from '../components/AdminLayout';
import logger from '../services/logService';
import DeleteConfirmationModal from '../components/DeleteConfirmationModal';
import TabNavigation from '../components/admin/TabNavigation';
import ModelosSection from '../components/admin/ModelosSection';
import IndependientesSection from '../components/admin/IndependientesSection';
import BannersSection from '../components/admin/BannersSection';
import OfertasSection from '../components/admin/OfertasSection';
import ConfigSection from '../components/admin/ConfigSection';

const AdminPage = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const getInitialTab = useCallback(() => {
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');
    return ['brujitas', 'independientes', 'banners', 'ofertas', 'config'].includes(tabParam) ? tabParam : 'brujitas';
  }, [location.search]);

  const [activeTab, setActiveTab] = useState(getInitialTab);

  const [data, setData] = useState({
    modelos: [],
    independientes: [],
    banners: [],
    ofertas: [],
    config: [],
  });

  const [loadingStates, setLoadingStates] = useState({
    brujitas: true,
    independientes: true,
    banners: true,
    ofertas: true,
    config: true,
  });

  const [errorStates, setErrorStates] = useState({
    brujitas: null,
    independientes: null,
    banners: null,
    ofertas: null,
    config: null,
  });

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [itemTypeToDelete, setItemTypeToDelete] = useState(null);

  const [bannerDeleteModalOpen, setBannerDeleteModalOpen] = useState(false);
  const [bannerToDelete, setBannerToDelete] = useState(null);
  const [isDeletingBanner, setIsDeletingBanner] = useState(false);

  const handleTabChange = (tab) => {
    if (tab === activeTab) return;

    // Determine the correct key for accessing the data array
    // data state keys: modelos, independientes, banners, ofertas
    // tab values (and loadingStates keys): brujitas, independientes, banners, ofertas
    const dataKey = tab === 'brujitas' ? 'modelos' : tab;

    // Only set loading state to true if data for that tab hasn't been loaded yet
    if (!data[dataKey] || data[dataKey].length === 0) {
      setLoadingStates(prev => ({ ...prev, [tab]: true })); // Use the 'tab' value as key for loadingStates
    }

    setActiveTab(tab);
    navigate(`/admin?tab=${tab}`, { replace: true });
  };

  const fetchModelos = useCallback(async () => {
    // setLoadingStates(prev => ({ ...prev, brujitas: true })); // Removed: loading state is set by handleTabChange or initial state
    setErrorStates(prev => ({ ...prev, brujitas: null }));
    try {
      const response = await modelosService.getAll();
      setData(prev => ({ ...prev, modelos: response.items || [] }));
    } catch (err) {
      logger.error('Error al cargar modelos:', err);
      setErrorStates(prev => ({ ...prev, brujitas: 'No se pudieron cargar los modelos.' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, brujitas: false }));
    }
  }, []);

  const fetchIndependientes = useCallback(async () => {
    // setLoadingStates(prev => ({ ...prev, independientes: true })); // Removed
    setErrorStates(prev => ({ ...prev, independientes: null }));
    try {
      const response = await independientesService.getAll();
      setData(prev => ({ ...prev, independientes: response.items || [] }));
    } catch (err) {
      logger.error('Error al cargar independientes:', err);
      setErrorStates(prev => ({ ...prev, independientes: 'No se pudieron cargar las independientes.' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, independientes: false }));
    }
  }, []);

  const fetchBanners = useCallback(async () => {
    // setLoadingStates(prev => ({ ...prev, banners: true })); // Removed
    setErrorStates(prev => ({ ...prev, banners: null }));
    try {
      const response = await bannerService.getAll();
      setData(prev => ({ ...prev, banners: response.items || [] }));
    } catch (err) {
      logger.error('Error al cargar banners:', err);
      setErrorStates(prev => ({ ...prev, banners: 'No se pudieron cargar los banners.' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, banners: false }));
    }
  }, []);

  const fetchOfertas = useCallback(async () => {
    // setLoadingStates(prev => ({ ...prev, ofertas: true })); // Removed
    setErrorStates(prev => ({ ...prev, ofertas: null }));
    try {
      const response = await ofertasService.getAll();
      setData(prev => ({ ...prev, ofertas: response.items || [] }));
    } catch (err) {
      logger.error('Error al cargar ofertas:', err);
      setErrorStates(prev => ({ ...prev, ofertas: 'No se pudieron cargar las ofertas.' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, ofertas: false }));
    }
  }, []);

  const fetchConfig = useCallback(async () => {
    setErrorStates(prev => ({ ...prev, config: null }));
    try {
      const response = await configService.getConfig();
      setData(prev => ({ ...prev, config: response.items || [] }));
    } catch (err) {
      logger.error('Error al cargar configuración:', err);
      setErrorStates(prev => ({ ...prev, config: 'No se pudo cargar la configuración.' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, config: false }));
    }
  }, []);

  useEffect(() => {
    const tabToFetchMap = {
      brujitas: { fetch: fetchModelos, dataKey: 'modelos' },
      independientes: { fetch: fetchIndependientes, dataKey: 'independientes' },
      banners: { fetch: fetchBanners, dataKey: 'banners' },
      ofertas: { fetch: fetchOfertas, dataKey: 'ofertas' },
      config: { fetch: fetchConfig, dataKey: 'config' },
    };

    const currentTabConfig = tabToFetchMap[activeTab];
    // Fetch only if data is empty to avoid infinite loops
    if (currentTabConfig && data[currentTabConfig.dataKey].length === 0) {
      currentTabConfig.fetch();
    }
  }, [activeTab, fetchModelos, fetchIndependientes, fetchBanners, fetchOfertas, fetchConfig]); // Removed data and loadingStates from dependencies

  useEffect(() => {
    const currentUrlTab = getInitialTab();
    if (currentUrlTab !== activeTab) {
        setActiveTab(currentUrlTab);
    }
  }, [location.search, activeTab, getInitialTab]);

  const openDeleteModal = (item, type) => {
    setItemToDelete(item);
    setItemTypeToDelete(type);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setItemToDelete(null);
    setItemTypeToDelete(null);
  };

  const handleDeleteItem = async () => {
    if (!itemToDelete || !itemTypeToDelete) return;

    const service = itemTypeToDelete === 'modelo' ? modelosService : independientesService;
    const dataKey = itemTypeToDelete === 'modelo' ? 'modelos' : 'independientes';
    const errorKey = itemTypeToDelete === 'modelo' ? 'brujitas' : 'independientes';
    const loadingKey = itemTypeToDelete === 'modelo' ? 'brujitas' : 'independientes';


    setLoadingStates(prev => ({ ...prev, [loadingKey]: true })); // Indicate deletion in progress

    try {
      await service.delete(itemToDelete.id);
      setData(prev => ({
        ...prev,
        [dataKey]: prev[dataKey].filter(item => item.id !== itemToDelete.id),
      }));
      closeDeleteModal();
    } catch (err) {
      logger.error(`Error al eliminar ${itemTypeToDelete}:`, err);
      setErrorStates(prev => ({
        ...prev,
        [errorKey]: `No se pudo eliminar ${itemTypeToDelete === 'modelo' ? 'la brujita' : 'la independiente'}.`,
      }));
    } finally {
        setLoadingStates(prev => ({ ...prev, [loadingKey]: false }));
    }
  };

  const handleDeleteBanner = async () => {
    if (!bannerToDelete) return;
    setIsDeletingBanner(true);
    setErrorStates(prev => ({ ...prev, banners: null }));
    try {
      await bannerService.delete(bannerToDelete.id);
      fetchBanners();
      setBannerDeleteModalOpen(false);
      setBannerToDelete(null);
    } catch (err) {
      logger.error('Error al eliminar banner:', err);
      setErrorStates(prev => ({ ...prev, banners: 'No se pudo eliminar el banner.' }));
    } finally {
      setIsDeletingBanner(false);
    }
  };

  const handleToggleOfertaVisibility = async (ofertaId, newVisibility) => {
    setErrorStates(prev => ({ ...prev, ofertas: null }));
    try {
      await ofertasService.toggleVisibility(ofertaId, newVisibility);
      setData(prevData => ({
        ...prevData,
        ofertas: prevData.ofertas.map(o =>
          o.id === ofertaId ? { ...o, visible: newVisibility } : o
        ),
      }));
    } catch (err) {
      logger.error('Error al cambiar visibilidad de oferta:', err);
      setErrorStates(prev => ({ ...prev, ofertas: 'No se pudo cambiar la visibilidad de la oferta.' }));
    }
  };

  const handleDeleteOferta = async (ofertaId) => {
    setErrorStates(prev => ({ ...prev, ofertas: null }));
    try {
      await ofertasService.delete(ofertaId);
      setData(prevData => ({
        ...prevData,
        ofertas: prevData.ofertas.filter(o => o.id !== ofertaId),
      }));
    } catch (err) {
      logger.error('Error al eliminar oferta:', err);
      setErrorStates(prev => ({ ...prev, ofertas: 'No se pudo eliminar la oferta.' }));
    }
  };

  const handleDisponibilidadChange = async (item, type) => {
    const dataKey = type === 'modelo' ? 'modelos' : 'independientes';
    const errorKey = type === 'modelo' ? 'brujitas' : 'independientes';
    setErrorStates(prev => ({ ...prev, [errorKey]: null }));

    try {
      const currentDisponibilidad = item.disponibilidad || 'Disponible';
      const newDisponibilidad = currentDisponibilidad === 'Disponible' ? 'No disponible' : 'Disponible';

      const formData = new FormData();
      formData.append('disponibilidad', newDisponibilidad);

      const service = type === 'modelo' ? modelosService : independientesService;
      await service.update(item.id, formData);

      setData(prev => ({
        ...prev,
        [dataKey]: prev[dataKey].map(currentItem =>
          currentItem.id === item.id ? { ...currentItem, disponibilidad: newDisponibilidad } : currentItem
        ),
      }));
    } catch (err) {
      logger.error(`Error al actualizar disponibilidad de ${type}:`, err);
      setErrorStates(prev => ({
        ...prev,
        [errorKey]: `No se pudo actualizar la disponibilidad.`,
      }));
    }
  };

  const getImageUrl = (item, type) => {
    const imagenField = item.imagen_perfil || item.profile_image || item.image || item.foto || item.photo;
    if (!item || !imagenField) return '/img/placeholder.png';

    if (item.collectionId && item.id && imagenField) {
        const service = type === 'modelo' ? modelosService : independientesService;
        if (service && typeof service.getImageUrl === 'function') {
             return service.getImageUrl(item, imagenField);
        }
    }
    return '/img/placeholder.png';
  };

  return (
    <AdminLayout>
      <TabNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        loadingStates={loadingStates}
      />

      {activeTab === 'brujitas' && (
        <ModelosSection
          modelos={data.modelos}
          loading={loadingStates.brujitas}
          error={errorStates.brujitas}
          onDisponibilidadChange={handleDisponibilidadChange}
          onDelete={openDeleteModal}
          getImageUrl={getImageUrl}
        />
      )}

      {activeTab === 'independientes' && (
        <IndependientesSection
          independientes={data.independientes}
          loading={loadingStates.independientes}
          error={errorStates.independientes}
          onDisponibilidadChange={handleDisponibilidadChange}
          onDelete={openDeleteModal}
          getImageUrl={getImageUrl}
        />
      )}

      {activeTab === 'banners' && (
        <BannersSection
          banners={data.banners}
          loading={loadingStates.banners}
          error={errorStates.banners}
          onRefresh={fetchBanners}
          onDeleteBanner={(banner) => { // Renamed from onDelete to avoid conflict
            setBannerToDelete(banner);
            setBannerDeleteModalOpen(true);
          }}
        />
      )}

      {activeTab === 'ofertas' && (
        <OfertasSection
          ofertas={data.ofertas}
          loading={loadingStates.ofertas}
          error={errorStates.ofertas}
          onToggleVisibility={handleToggleOfertaVisibility}
          onDeleteOferta={handleDeleteOferta}
          setOfertaError={(msg) => setErrorStates(prev => ({...prev, ofertas: msg}))}
        />
      )}

      {activeTab === 'config' && (
        <ConfigSection
          config={data.config}
          loading={loadingStates.config}
          error={errorStates.config}
          onSave={fetchConfig}
        />
      )}

      {deleteModalOpen && (
        <DeleteConfirmationModal
          isOpen={deleteModalOpen}
          onClose={closeDeleteModal}
          onConfirm={handleDeleteItem}
          title={`Confirmar eliminación de ${itemTypeToDelete === 'modelo' ? 'brujita' : 'independiente'}`}
          message={`¿Estás seguro de que deseas eliminar a ${itemToDelete?.nombre}? Esta acción no se puede deshacer.`}
          isDeleting={itemTypeToDelete && loadingStates[itemTypeToDelete === 'modelo' ? 'brujitas' : 'independientes']}
        />
      )}

      {bannerDeleteModalOpen && (
        <DeleteConfirmationModal
          isOpen={bannerDeleteModalOpen}
          onClose={() => {
            setBannerDeleteModalOpen(false);
            setBannerToDelete(null);
          }}
          onConfirm={handleDeleteBanner}
          title="Confirmar eliminación de banner"
          message={`¿Estás seguro de que deseas eliminar el banner "${bannerToDelete?.titulo}"? Esta acción no se puede deshacer.`}
          isDeleting={isDeletingBanner}
        />
      )}
    </AdminLayout>
  );
};

export default AdminPage;
