import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { independientesService } from '../services/independientesService'; // Assuming this is the correct path
import AdminLayout from '../components/AdminLayout';
import IndependienteForm from '../components/IndependienteForm';
import logger from '../services/logService';

const EditIndependientePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [independiente, setIndependiente] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchIndependiente = async () => {
      try {
        setLoading(true);
        const data = await independientesService.getById(id);
        setIndependiente(data);
      } catch (err) {
        logger.error('Error al cargar la independiente:', err);
        setError('No se pudo cargar la información de la independiente. Por favor, intenta de nuevo más tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchIndependiente();
  }, [id]);

  const handleSubmit = () => {
    // Redirigir a la página de administración con la pestaña de independientes activa
    navigate('/admin?tab=independientes');
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="bg-red-50 p-4 rounded-md">
          <p className="text-red-500">{error}</p>
          <button
            onClick={() => navigate('/admin?tab=independientes')}
            className="mt-4 text-blue-600 hover:underline"
          >
            Volver a administración
          </button>
        </div>
      </AdminLayout>
    );
  }

  if (!independiente) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No se encontró la independiente solicitada.</p>
          <button
            onClick={() => navigate('/admin?tab=independientes')}
            className="mt-4 text-blue-600 hover:underline"
          >
            Volver a administración
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#e0b3ff] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#9c27b0]">✨</span> Editar Independiente: <span className="text-white">{independiente.nombre}</span>
        </h1>
      </div>

      <IndependienteForm modelo={independiente} onSubmit={handleSubmit} />
    </AdminLayout>
  );
};

export default EditIndependientePage;
