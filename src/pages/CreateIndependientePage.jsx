import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import IndependienteForm from '../components/IndependienteForm';

const CreateIndependientePage = () => {
  const navigate = useNavigate();

  const handleSubmit = () => {
    // Redirigir a la página de administración con la pestaña de independientes activa
    navigate('/admin?tab=independientes');
  };

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#e0b3ff] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#9c27b0]">✨</span>Nueva Independiente
        </h1>
      </div>

      <IndependienteForm onSubmit={handleSubmit} />
    </Layout>
  );
};

export default CreateIndependientePage;
