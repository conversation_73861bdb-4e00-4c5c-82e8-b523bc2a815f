import { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { modelosService } from '../services/pocketbase';
import Layout from '../components/Layout';
import ImageGallery from '../components/ImageGallery';
import DebugComponent from '../components/DebugComponent';
import logger from '../services/logService';

const ModeloDetailPage = () => {
  const { id } = useParams();
  const [modelo, setModelo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchModelo = async () => {
      try {
        setLoading(true);
        const data = await modelosService.getById(id);
        setModelo(data);
      } catch (err) {
        logger.error('Error al cargar el modelo:', err);
        setError('No se pudo cargar la información del modelo. Por favor, intenta de nuevo más tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchModelo();
  }, [id]);

  // Obtener URLs de las imágenes
  const getImageUrl = (fieldName) => {
    if (!modelo) return '/img/placeholder.png';

    // Buscar el campo en diferentes posibles nombres
    const fileName =
      fieldName === 'imagen_perfil' ?
        (modelo.imagen_perfil || modelo.profile_image || modelo.photo) :
      fieldName === 'imagen_destacada' ?
        (modelo.imagen_destacada || modelo.featured_image || modelo.image) :
        modelo[fieldName];

    if (!fileName) return '/img/placeholder.png';

    logger.debug(`Obteniendo URL para ${fieldName}:`, fileName);
    return modelosService.getImageUrl(modelo, fileName);
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-50 p-4 rounded-md">
          <p className="text-red-500">{error}</p>
          <Link to="/" className="mt-4 inline-block text-blue-600 hover:underline">
            Regresar
          </Link>
        </div>
      </Layout>
    );
  }

  if (!modelo) {
    return (
      <Layout>
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No se encontró el modelo solicitado.</p>
          <Link to="/" className="mt-4 inline-block text-blue-600 hover:underline">
          Regresar
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mb-6">
        <Link to="/" className="text-blue-600 hover:underline flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Regresar
        </Link>
      </div>

      {/* Componente de depuración */}
      {!loading && !error && modelo && (
        <DebugComponent data={modelo} title="Datos del Modelo" />
      )}

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Cabecera con imagen de perfil y datos principales */}
        <div className="md:flex">
          <div className="md:w-1/3">
            <img
              src={getImageUrl('imagen_perfil') || getImageUrl('imagen_destacada') || '/img/placeholder.png'}
              alt={`Modelo ${modelo.nombre || 'Sin nombre'}`}
              className="w-full h-full object-cover object-center"
              onError={(e) => {
                logger.debug('Error al cargar imagen, usando placeholder');
                e.target.src = '/img/placeholder.png';
              }}
            />
          </div>

          <div className="p-6 md:w-2/3">
            <div className="flex justify-between items-start">
              <h1 className="text-3xl font-bold text-gray-800">{modelo.nombre}</h1>

              {modelo.estado && (
                <span className={`px-3 py-1 text-sm rounded-full ${
                  modelo.estado === 'Destacado'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-purple-100 text-purple-800'
                }`}>
                  {modelo.estado}
                </span>
              )}
            </div>

            <div className="mt-4 grid grid-cols-2 gap-4">
              <div>
                <p className="text-gray-500">Edad</p>
                <p className="text-lg font-medium">{modelo.edad} años</p>
              </div>

              <div>
                <p className="text-gray-500">Perfil Corporal</p>
                <p className="text-lg font-medium">{modelo.perfil_corporal}</p>
              </div>

              {/* Medidas - pueden estar como propiedades directas o como objeto */}
              {((modelo.busto || modelo.cintura || modelo.cadera ||
                 (modelo.medidas && (modelo.medidas.busto || modelo.medidas.cintura || modelo.medidas.cadera)))) && (
                <div className="col-span-2">
                  <p className="text-gray-500">Medidas (cm)</p>
                  <p className="text-lg font-medium">
                    {/* Buscar en propiedades directas o en el objeto medidas */}
                    {(modelo.busto || (modelo.medidas && modelo.medidas.busto)) &&
                      `Busto: ${modelo.busto || modelo.medidas.busto}`}

                    {(modelo.cintura || (modelo.medidas && modelo.medidas.cintura)) &&
                     (modelo.busto || (modelo.medidas && modelo.medidas.busto)) && ' / '}

                    {(modelo.cintura || (modelo.medidas && modelo.medidas.cintura)) &&
                      `Cintura: ${modelo.cintura || modelo.medidas.cintura}`}

                    {(modelo.cadera || (modelo.medidas && modelo.medidas.cadera)) &&
                     ((modelo.busto || (modelo.medidas && modelo.medidas.busto)) ||
                      (modelo.cintura || (modelo.medidas && modelo.medidas.cintura))) && ' / '}

                    {(modelo.cadera || (modelo.medidas && modelo.medidas.cadera)) &&
                      `Cadera: ${modelo.cadera || modelo.medidas.cadera}`}
                  </p>
                </div>
              )}
            </div>

            {modelo.tags && modelo.tags.length > 0 && (
              <div className="mt-4">
                <p className="text-gray-500 mb-1">Especialidades</p>
                <div className="flex flex-wrap gap-2">
                  {Array.isArray(modelo.tags)
                    ? modelo.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded-md">
                          {tag}
                        </span>
                      ))
                    : modelo.tags.split(',').map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded-md">
                          {tag.trim()}
                        </span>
                      ))
                  }
                </div>
              </div>
            )}

            {modelo.biografia && (
              <div className="mt-6">
                <h2 className="text-xl font-semibold mb-2">Biografía</h2>
                <p className="text-gray-700 whitespace-pre-line">{modelo.biografia}</p>
              </div>
            )}
          </div>
        </div>

        {/* Galería de imágenes */}
        <div className="p-6 border-t border-gray-200">
          <h2 className="text-xl font-semibold mb-4">Galería</h2>
          <ImageGallery modelo={modelo} />
        </div>
      </div>
    </Layout>
  );
};

export default ModeloDetailPage;
