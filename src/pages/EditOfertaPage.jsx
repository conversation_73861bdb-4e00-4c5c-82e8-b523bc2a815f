import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import OfertaForm from '../components/OfertaForm';
import { ofertasService } from '../services/ofertasService';
import logger from '../services/logService';

const EditOfertaPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [oferta, setOferta] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchOferta = async () => {
      try {
        setLoading(true);
        const data = await ofertasService.getById(id);
        setOferta(data);
      } catch (err) {
        logger.error('Error al cargar oferta:', err);
        setError('No se pudo cargar la oferta. Por favor, intenta de nuevo más tarde.');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchOferta();
    }
  }, [id]);

  const handleSubmit = () => {
    // Redirigir a la página de administración con la pestaña de ofertas activa
    navigate('/admin?tab=ofertas');
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-50 p-4 rounded-md">
          <p className="text-red-500">{error}</p>
          <button
            onClick={() => navigate('/admin?tab=ofertas')}
            className="mt-4 text-blue-600 hover:underline"
          >
            Volver a administración
          </button>
        </div>
      </Layout>
    );
  }

  if (!oferta) {
    return (
      <Layout>
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No se encontró la oferta solicitada.</p>
          <button
            onClick={() => navigate('/admin?tab=ofertas')}
            className="mt-4 text-blue-600 hover:underline"
          >
            Volver a administración
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#e0b3ff] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#9c27b0]">🔥</span> Editar Oferta: <span className="text-white">{oferta.titulo}</span>
        </h1>
      </div>

      <OfertaForm oferta={oferta} onSubmit={handleSubmit} />
    </Layout>
  );
};

export default EditOfertaPage;
