import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { bannerService } from '../services/bannerService';
import Layout from '../components/Layout';
import BannerForm from '../components/BannerForm';
import logger from '../services/logService';

const EditBannerPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [banner, setBanner] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBanner = async () => {
      try {
        setLoading(true);
        const data = await bannerService.getById(id);
        setBanner(data);
      } catch (err) {
        logger.error('Error al cargar el banner:', err);
        setError('No se pudo cargar la información del banner. Por favor, intenta de nuevo más tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchBanner();
  }, [id]);

  const handleSubmit = () => {
    // Redirigir a la sección de banners después de actualizar
    navigate('/admin?tab=banners');
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex flex-col justify-center items-center h-64">
          <div className="w-16 h-16 relative animate-pulse-glow">
            <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#9c27b0] animate-spin"></div>
            <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#e91e63] animate-spin animate-reverse"></div>
          </div>
          <p className="mt-4 text-gray-400">Cargando banner...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-[#2d0a16] p-6 rounded-lg border border-red-800 mb-6">
          <h3 className="text-xl font-semibold text-red-400 mb-2">Error</h3>
          <p className="text-red-300">{error}</p>
          <button
            onClick={() => navigate('/admin?tab=banners')}
            className="mt-4 px-4 py-2 bg-[#23233a] text-gray-300 rounded-md hover:bg-[#29293d] transition-colors"
          >
            Volver a administración
          </button>
        </div>
      </Layout>
    );
  }

  if (!banner) {
    return (
      <Layout>
        <div className="bg-[#181828] p-8 rounded-lg shadow-lg text-center border border-[#29293d]">
          <p className="text-gray-300 mb-4">No se encontró el banner solicitado.</p>
          <button
            onClick={() => navigate('/admin?tab=banners')}
            className="px-6 py-3 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-lg hover:shadow-lg hover:shadow-[#9c27b0]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300"
          >
            Volver a administración
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#e0b3ff] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#9c27b0]">🖼️</span> Editar Banner: <span className="text-white">{banner.titulo}</span>
        </h1>
      </div>

      <BannerForm
        banner={banner}
        onSubmit={handleSubmit}
        onCancel={() => navigate('/admin?tab=banners')}
      />
    </Layout>
  );
};

export default EditBannerPage;
