import { useNavigate } from 'react-router-dom';
import AdminLayout from '../components/AdminLayout';
import OfertaForm from '../components/OfertaForm';

const CreateOfertaPage = () => {
  const navigate = useNavigate();

  const handleSubmit = () => {
    // Redirigir a la página de administración con la pestaña de ofertas activa
    navigate('/admin?tab=ofertas');
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#e0b3ff] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#9c27b0]">🔥</span>Nueva Oferta
        </h1>
      </div>

      <OfertaForm onSubmit={handleSubmit} />
    </AdminLayout>
  );
};

export default CreateOfertaPage;
