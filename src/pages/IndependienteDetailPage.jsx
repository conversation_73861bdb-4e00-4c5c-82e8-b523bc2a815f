import { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { independientesService } from '../services/independientesService';
import Layout from '../components/Layout';
import GaleriaModal from '../components/GaleriaModal';
import AppLayout from '../components/AppLayout';
import logger from '../services/logService';
import visitasService from '../services/visitasService';
import LazyImage from '../components/LazyImage';
import RecomendadosSection from '../components/RecomendadosSection';
import { useConfig } from '../context/ConfigContext';

const IndependienteDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [independiente, setIndependiente] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showGaleria, setShowGaleria] = useState(true);
  const [showFullBio, setShowFullBio] = useState(false);
  const [galeriaModalOpen, setGaleriaModalOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showContactOptions, setShowContactOptions] = useState(false);
  const { wa, telegram } = useConfig();

  // Efecto para cerrar el menú de contacto cuando se hace clic fuera de él
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Si el menú está abierto y el clic no fue dentro del menú o del botón
      if (showContactOptions) {
        const contactButtons = document.querySelectorAll('.contact-button-container');
        let clickedInsideButton = false;

        contactButtons.forEach(button => {
          if (button.contains(event.target)) {
            clickedInsideButton = true;
          }
        });

        if (!clickedInsideButton) {
          setShowContactOptions(false);
        }
      }
    };

    // Agregar el event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Limpiar el event listener cuando el componente se desmonte
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showContactOptions]);

  // Efecto para cargar la independiente
  useEffect(() => {
    const fetchIndependiente = async () => {
      try {
        setLoading(true);
        const data = await independientesService.getById(id);
        setIndependiente(data);

        // Registrar la visita
        try {
          await visitasService.registrarVisita(id, 'independiente');
        } catch (visitaError) {
          // Si hay un error al registrar la visita, solo lo registramos pero no afecta la experiencia del usuario
          logger.error('Error al registrar visita:', visitaError);
        }
      } catch (err) {
        logger.error('Error al cargar la independiente:', err);
        setError('No se pudo cargar la información de la independiente. Por favor, intenta de nuevo más tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchIndependiente();
  }, [id]);

  // Efecto para redirigir si la independiente no está disponible
  useEffect(() => {
    if (independiente && !loading && !error && !isDisponible()) {
      navigate('/independientes');
    }
  }, [independiente, loading, error, navigate]);

  // Obtener URLs de las imágenes
  const getImageUrl = (fieldName) => {
    if (!independiente) return '/img/placeholder.png';

    // Buscar el campo en diferentes posibles nombres
    const fileName =
      fieldName === 'imagen_perfil' ?
        (independiente.imagen_perfil || independiente.profile_image || independiente.photo) :
      fieldName === 'imagen_destacada' ?
        (independiente.imagen_destacada || independiente.featured_image || independiente.image) :
        independiente[fieldName];

    if (!fileName) return '/img/placeholder.png';

    logger.debug(`Obteniendo URL para ${fieldName}:`, fileName);
    // Assuming independientesService has a similar getImageUrl method
    return independientesService.getImageUrl(independiente, fileName);
  };

  if (loading) {
    return (
      <AppLayout>
        <Layout>
          <div className="flex flex-col justify-center items-center h-64">
            <div className="w-16 h-16 relative animate-pulse-glow">
              <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#3f51b5] animate-spin"></div>
              <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#00bcd4] animate-spin animate-reverse"></div>
            </div>
            <p className="mt-4 text-gray-400">Cargando información...</p>
          </div>
        </Layout>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <Layout>
          <div className="bg-[#2d0a16] p-6 rounded-lg border border-red-800">
            <h3 className="text-xl font-semibold text-red-400 mb-2">Error</h3>
            <p className="text-red-300">{error}</p>
            <Link to="/independientes" className="mt-4 px-4 py-2 bg-red-800 hover:bg-red-700 text-white rounded-md transition-colors inline-block">
            Regresar
            </Link>
          </div>
        </Layout>
      </AppLayout>
    );
  }

  if (!independiente) {
    return (
      <AppLayout>
        <Layout>
          <div className="flex flex-col items-center justify-center py-12">
            <div className="w-full max-w-md p-8 rounded-xl shadow-lg border bg-[#1e1e2d] border-[#3d3d5a]">
              {/* Icono de error */}
              <div className="flex justify-center mb-6">
                <div className="w-24 h-24 rounded-full bg-[#2d1949] flex items-center justify-center text-5xl animate-pulse-slow">
                  😔
                </div>
              </div>

              {/* Título y mensaje */}
              <h3 className="text-2xl font-bold text-center mb-4 text-[#e0b3ff]">
                Independiente no encontrada
              </h3>
              <p className="text-center mb-6 text-gray-300">
                No se encontró la independiente solicitada o ha sido eliminada.
              </p>

              <div className="flex justify-center">
                <Link
                  to="/independientes"
                  className="px-6 py-3 bg-gradient-to-r from-[#3f51b5] to-[#00bcd4] text-white rounded-lg hover:shadow-lg transition-all"
                >
                 Regresar
                </Link>
              </div>
            </div>
          </div>
        </Layout>
      </AppLayout>
    );
  }

  // Extraer datos para mostrar
  const nombre = independiente.nombre || 'Sin nombre';
  const edad = independiente.edad || '';
  const perfilCorporal = independiente.perfil_corporal || '';
  const altura = independiente.altura || '';
  const biografia = independiente.biografia || '';

  // Función para verificar disponibilidad
  const isDisponible = () => {
    if (!independiente) return true; // Si no hay datos aún, asumimos que está disponible

    // Verificar disponibilidad en diferentes formatos posibles
    if (independiente.disponibilidad === 'No disponible') return false;
    if (independiente.disponible === false) return false;

    // Si llegamos aquí, la independiente está disponible
    return true;
  };

  // Verificar si la independiente es VIP o Destacada
  let isVIP = false;
  let isDestacada = false;

  // Verificar campos booleanos directos
  if (independiente.vip === true) isVIP = true;
  if (independiente.destacada === true) isDestacada = true;

  // Verificar en el campo estado (puede ser string o array)
  const estado = independiente.estado || [];
  if (typeof estado === 'string') {
    isVIP = isVIP || estado === 'VIP' || estado.includes('VIP');
    isDestacada = isDestacada || estado === 'Destacada' || estado.includes('Destacada');
  } else if (Array.isArray(estado)) {
    isVIP = isVIP || estado.includes('VIP');
    isDestacada = isDestacada || estado.includes('Destacada');
  } else if (typeof estado === 'object' && estado !== null) {
    // Si es un objeto, verificar si tiene propiedades VIP o Destacada
    isVIP = isVIP || estado.VIP || estado.vip || false;
    isDestacada = isDestacada || estado.Destacada || estado.destacada || false;
  }

  // Registrar en modo debug
  logger.debug(`Independiente ${nombre} (${independiente.id}) - Estado:`, {
    isVIP,
    isDestacada,
    isDisponible: isDisponible(),
    estado
  });

  // Procesar zonas de trabajo
  const zonas = (() => {
    try {
      if (independiente.zonas) {
        if (typeof independiente.zonas === 'string') {
          return JSON.parse(independiente.zonas);
        }
        if (Array.isArray(independiente.zonas)) {
          return independiente.zonas;
        }
      }
      return [];
    } catch (error) {
      logger.error('Error al parsear zonas:', error);
      return [];
    }
  })();

  return (
    <AppLayout>
      <Layout>
        <div className="mb-6">
          <Link to="/independientes" className="text-gray-300 hover:text-white flex items-center transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Regresar
          </Link>
        </div>

        {/* Modal de galería de imágenes */}
        <GaleriaModal
          isOpen={galeriaModalOpen}
          onClose={() => setGaleriaModalOpen(false)}
          brujita={independiente}
          initialImageIndex={selectedImageIndex}
        />

        {/* Contenido principal */}
        <div className="relative">
          {/* Imagen de fondo con overlay */}
          <div className="absolute inset-0 h-96 overflow-hidden z-0 rounded-xl">
            <div className="absolute inset-0 bg-gradient-to-b from-[#3f51b5]/20 to-[#121212] z-10"></div>
            <LazyImage
              src={getImageUrl('imagen_perfil') || '/img/placeholder.png'}
              alt=""
              className="w-full h-full opacity-30 blur-sm"
              objectFit="cover"
            />
          </div>

          {/* Contenido principal */}
          <div className="relative z-10 pt-10">
            <div className="flex flex-col md:flex-row gap-8 items-start">
              {/* Imagen principal */}
              <div className="md:w-1/3">
                <div className="rounded-xl overflow-hidden border-4 border-[#3f51b5]/30 shadow-xl">
                  <LazyImage
                    src={getImageUrl('imagen_perfil') || '/img/placeholder.png'}
                    alt={`Independiente ${nombre}`}
                    className="w-full aspect-[3/4]"
                    objectFit="cover"
                    onError={(e) => {
                      e.target.src = '/img/placeholder.png';
                    }}
                  />
                </div>

                {/* Botón de contacto - solo visible en desktop */}
                <div className="hidden md:block">
                  {/* Botón de contacto con opciones desplegables */}
                  <div className="mt-4 relative contact-button-container">
                    <button
                      onClick={() => setShowContactOptions(!showContactOptions)}
                      className="w-full flex items-center justify-center py-3 px-4 bg-gradient-to-r from-[#3f51b5] to-[#00bcd4] text-white rounded-lg hover:opacity-90 transition-all"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                        <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
                      </svg>
                      Solicitar número {['🥵', '🥰', '🤩'][Math.floor(Math.random() * 3)]}
                    </button>

                    {/* Opciones de contacto desplegables */}
                    {showContactOptions && (
                      <div className="absolute left-0 right-0 mt-2 bg-[#1e1e1e] border border-[#3d3d5a] rounded-lg shadow-lg overflow-hidden z-30 animate-fade-in">
                        {wa && (
                          <a
                            href={wa}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center px-4 py-3 hover:bg-[#2d2d2d] transition-colors"
                            onClick={() => setShowContactOptions(false)}
                          >
                            <div className="w-8 h-8 rounded-full bg-[#25D366] bg-opacity-20 flex items-center justify-center mr-3">
                              <svg className="w-5 h-5 text-[#25D366]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                                <path fill="currentColor" d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
                              </svg>
                            </div>
                            <span className="text-white">Contactar por WhatsApp</span>
                          </a>
                        )}

                        {telegram && (
                          <a
                            href={telegram}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center px-4 py-3 hover:bg-[#2d2d2d] transition-colors"
                            onClick={() => setShowContactOptions(false)}
                          >
                            <div className="w-8 h-8 rounded-full bg-[#0088cc] bg-opacity-20 flex items-center justify-center mr-3">
                              <svg className="w-5 h-5 text-[#0088cc]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512">
                                <path fill="currentColor" d="M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm121.8 169.9l-40.7 191.8c-3 13.6-11.1 16.9-22.4 10.5l-62-45.7-29.9 28.8c-3.3 3.3-6.1 6.1-12.5 6.1l4.4-63.1 114.9-103.8c5-4.4-1.1-6.9-7.7-2.5l-142 89.4-61.2-19.1c-13.3-4.2-13.6-13.3 2.8-19.7l239.1-92.2c11.1-4 20.8 2.7 17.2 19.5z"/>
                              </svg>
                            </div>
                            <span className="text-white">Contactar por Telegram</span>
                          </a>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Información principal */}
              <div className="md:w-2/3">
                <div>
                  <div className="flex justify-between items-start">
                    <h1 className="text-3xl md:text-4xl font-bold text-white mb-1">
                      {nombre} {edad && <span className="text-gray-400 text-2xl">• {edad} años</span>}
                    </h1>

                    {/* Contador de visitas */}
                    <div className="flex items-center text-sm text-gray-400 bg-[#1a237e]/50 px-3 py-1 rounded-lg border border-[#3f51b5]/30">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-[#64b5f6]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span className="font-medium">{independiente.visitas || 0}</span>
                      <span className="ml-1 text-xs text-gray-500">visitas</span>
                    </div>
                  </div>

                  {/* Tags de VIP/Destacada */}
                  <div className="flex flex-wrap items-center gap-2 text-gray-300 mb-3">
                    {isVIP && (
                      <span className="badge-vip">
                        <span>⭐</span> VIP
                      </span>
                    )}
                    {isDestacada && (
                      <span className="badge-destacado">
                        <span>🔥</span> Destacada
                      </span>
                    )}
                  </div>

                  {/* Perfil corporal y altura en una línea */}
                  <div className="flex flex-wrap items-center gap-2 text-gray-300 mb-2">
                    {perfilCorporal && (
                      <span className="bg-[#2d2d2d] px-3 py-1 rounded-full text-sm flex items-center gap-1">
                        <span>👗</span> {perfilCorporal}
                      </span>
                    )}
                    {altura && (
                      <span className="bg-[#2d2d2d] px-3 py-1 rounded-full text-sm flex items-center gap-1">
                        <span>📏</span> {altura} cm
                      </span>
                    )}
                  </div>

                  {/* Zonas de trabajo en otra línea */}
                  {zonas.length > 0 && (
                    <div className="flex flex-wrap items-center gap-2 text-gray-300 mb-3">
                      {zonas.map((zona, index) => (
                        <span
                          key={index}
                          className="bg-[#3f51b5]/30 px-3 py-1 rounded-full text-sm flex items-center gap-1"
                        >
                          <span>📍</span> {zona}
                        </span>
                      ))}
                    </div>
                  )}

                  {/* Botón de contacto - solo visible en móvil */}
                  <div className="block md:hidden mb-4">
                    {/* Botón de contacto con opciones desplegables - versión móvil */}
                    <div className="mb-3 relative contact-button-container">
                      <button
                        onClick={() => setShowContactOptions(!showContactOptions)}
                        className="w-full flex items-center justify-center py-3 px-4 bg-gradient-to-r from-[#3f51b5] to-[#00bcd4] text-white rounded-lg hover:opacity-90 transition-all"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                          <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
                        </svg>
                        Solicitar independiente {['🥵', '🥰', '🤩'][Math.floor(Math.random() * 3)]}
                      </button>

                      {/* Opciones de contacto desplegables */}
                      {showContactOptions && (
                        <div className="absolute left-0 right-0 mt-2 bg-[#1e1e1e] border border-[#3d3d5a] rounded-lg shadow-lg overflow-hidden z-30 animate-fade-in">
                          {wa && (
                            <a
                              href={wa}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center px-4 py-3 hover:bg-[#2d2d2d] transition-colors"
                              onClick={() => setShowContactOptions(false)}
                            >
                              <div className="w-8 h-8 rounded-full bg-[#25D366] bg-opacity-20 flex items-center justify-center mr-3">
                                <svg className="w-5 h-5 text-[#25D366]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                                  <path fill="currentColor" d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
                                </svg>
                              </div>
                              <span className="text-white">Contactar por WhatsApp</span>
                            </a>
                          )}

                          {telegram && (
                            <a
                              href={telegram}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center px-4 py-3 hover:bg-[#2d2d2d] transition-colors"
                              onClick={() => setShowContactOptions(false)}
                            >
                              <div className="w-8 h-8 rounded-full bg-[#0088cc] bg-opacity-20 flex items-center justify-center mr-3">
                                <svg className="w-5 h-5 text-[#0088cc]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512">
                                  <path fill="currentColor" d="M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm121.8 169.9l-40.7 191.8c-3 13.6-11.1 16.9-22.4 10.5l-62-45.7-29.9 28.8c-3.3 3.3-6.1 6.1-12.5 6.1l4.4-63.1 114.9-103.8c5-4.4-1.1-6.9-7.7-2.5l-142 89.4-61.2-19.1c-13.3-4.2-13.6-13.3 2.8-19.7l239.1-92.2c11.1-4 20.8 2.7 17.2 19.5z"/>
                                </svg>
                              </div>
                              <span className="text-white">Contactar por Telegram</span>
                            </a>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Sobre mí - versión compacta */}
                  {biografia && (
                    <div className="bg-[#2d2d2d] p-3 rounded-lg mb-4">
                      <h3 className="text-sm font-semibold text-white mb-1">Sobre mí</h3>
                      <p className="text-gray-300 text-sm whitespace-pre-line">
                        {showFullBio || biografia.length <= 150
                          ? biografia
                          : biografia.substring(0, 150) + '...'}
                      </p>
                      {biografia.length > 150 && (
                        <button
                          onClick={() => setShowFullBio(!showFullBio)}
                          className="mt-1 text-[#3f51b5] hover:text-[#00bcd4] text-xs font-medium transition-colors"
                        >
                          {showFullBio ? 'Mostrar menos' : 'Leer más'}
                        </button>
                      )}
                    </div>
                  )}

                  {/* Sección de acordeones con información adicional */}
                  <div className="space-y-3 mb-6">
                    {/* Acordeón de Horarios
                    <div className="bg-[#2d2d2d] rounded-lg overflow-hidden">
                      <details className="group">
                        <summary className="flex justify-between items-center p-3 cursor-pointer">
                          <h3 className="text-sm font-semibold text-white flex items-center">
                            <span className="mr-2">⏰</span> Horarios
                          </h3>
                          <span className="text-[#3f51b5] transition-transform group-open:rotate-180">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </span>
                        </summary>
                        <div className="p-3 pt-0 border-t border-[#3d3d5a] text-gray-300 text-sm">
                          <p className="font-semibold mb-1">Información y Atención al cliente:</p>
                          <p className="mb-3">Lunes a Domingo de 8:00 AM. a 12:00 PM.</p>

                          <p className="font-semibold mb-1">Servicios:</p>
                          <p className="mb-3">Solo se realizan servicios de 8:00 AM. a 9:00 PM.</p>

                          <p className="font-semibold mb-1">Nota:</p>
                          <p>Cada chica tiene su propio horario y días a la semana que trabaja.</p>
                        </div>
                      </details>
                    </div> */}

                    {/* Acordeón de Cargo extra
                    <div className="bg-[#2d2d2d] rounded-lg overflow-hidden">
                      <details className="group">
                        <summary className="flex justify-between items-center p-3 cursor-pointer">
                          <h3 className="text-sm font-semibold text-white flex items-center">
                            <span className="mr-2">💰</span> Cargo extra
                          </h3>
                          <span className="text-[#3f51b5] transition-transform group-open:rotate-180">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </span>
                        </summary>
                        <div className="p-3 pt-0 border-t border-[#3d3d5a] text-gray-300 text-sm">
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <span className="text-[#3f51b5] mr-2">✦</span>
                              <span>De 1 km. a 20 km. sin costo extra.</span>
                            </li>
                            <li className="flex items-start">
                              <span className="text-[#3f51b5] mr-2">✦</span>
                              <span>De 21 Km. a 30 km. $100 extras.</span>
                            </li>
                            <li className="flex items-start">
                              <span className="text-[#3f51b5] mr-2">✦</span>
                              <span>De 31 km. a 40 Km. $200 extras.</span>
                            </li>
                            <li className="flex items-start">
                              <span className="text-[#3f51b5] mr-2">✦</span>
                              <span>De 41 km. a 50 km. $300 extras.</span>
                            </li>
                          </ul>
                          <p className="mt-3">Estando en puntos más lejanos de nuestro perímetro antes señalado se consultara la zona, para ver si se puede realizar el servicio.</p>
                        </div>
                      </details>
                    </div> */}

                    {/* Acordeón de Zonas de trabajo
                    <div className="bg-[#2d2d2d] rounded-lg overflow-hidden">
                      <details className="group">
                        <summary className="flex justify-between items-center p-3 cursor-pointer">
                          <h3 className="text-sm font-semibold text-white flex items-center">
                            <span className="mr-2">📍</span> Zonas de trabajo
                          </h3>
                          <span className="text-[#3f51b5] transition-transform group-open:rotate-180">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </span>
                        </summary>
                        <div className="p-3 pt-0 border-t border-[#3d3d5a] text-gray-300 text-sm">
                          <p className="mb-3">Nuestra zona de trabajo tiene cobertura en la Ciudad de México y el Estado de México (solo zona metropolitana).</p>
                          <p>Las zonas que estan a más de 21 kilómetros de nuestro punto de partida (Tecamac, Edo. Mex.) tendrán un costo adicional debido a la distancia.</p>
                        </div>
                      </details>
                    </div> */}
                  </div>
                </div>

                {/* Galería de imágenes */}
                {independiente.galeria && independiente.galeria.length > 0 && (
                  <div className="mt-6">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-white">Galería de fotos</h2>
                      <button
                        onClick={() => setShowGaleria(!showGaleria)}
                        className="text-[#3f51b5] hover:text-[#00bcd4] text-sm font-medium transition-colors"
                      >
                        {showGaleria ? 'Ocultar' : 'Mostrar'}
                      </button>
                    </div>

                    {showGaleria && (
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                        {independiente.galeria.map((imagen, index) => (
                          <div
                            key={index}
                            className="aspect-square rounded-lg cursor-pointer hover:opacity-90 transition-opacity border border-[#3d3d5a] flex items-center justify-center bg-[#181828]"
                            onClick={() => {
                              setSelectedImageIndex(index);
                              setGaleriaModalOpen(true);
                            }}
                          >
                            <LazyImage
                              src={independientesService.getImageUrl(independiente, imagen)}
                              alt={`Foto ${index + 1} de ${nombre}`}
                              className="w-full h-full"
                              objectFit="cover"
                              onError={(e) => {
                                e.target.src = '/img/placeholder.png';
                              }}
                            />
                          </div>
                        ))}

                        {/* Cuadro informativo sobre fotos en lencería (al final de la galería)
                        <div className="aspect-square rounded-lg overflow-hidden border border-[#3f51b5] bg-gradient-to-br from-[#1a237e] to-[#1e1e1e] flex flex-col items-center justify-center text-center p-4">
                          <div className="bg-[#3f51b5] bg-opacity-20 rounded-full p-3 mb-3">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#00bcd4]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                          </div>
                          <h3 className="text-white font-bold mb-2">Fotos en lencería</h3>
                          <p className="text-cyan-300 text-sm mb-2">Aportación de 100$ por 5 fotos</p>
                          <p className="text-gray-300 text-xs">Cuenta como anticipo para tu servicio</p>
                        </div>*/}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Sección de recomendados */}
        {independiente && (
          <RecomendadosSection
            currentModelId={id}
            modelType="independiente"
            excludeTypes={[]}
            limit={3}
          />
        )}
      </Layout>
    </AppLayout>
  );
};

export default IndependienteDetailPage;
