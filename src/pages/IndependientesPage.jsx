import { useState, useEffect } from 'react';
import { independientesService } from '../services/independientesService';
import IndependienteCard from '../components/IndependienteCard';
import Layout from '../components/Layout';
import logger from '../services/logService';
import AppLayout from '../components/AppLayout';

const IndependientesPage = () => {
  const [independientes, setIndependientes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchIndependientes = async () => {
      try {
        setLoading(true);
        const response = await independientesService.getAll();
        logger.debug('Datos de independientes recibidos de la API:', response.items);
        setIndependientes(response.items);
      } catch (err) {
        logger.error('Error al cargar independientes:', err);
        setError('No se pudieron cargar las independientes. Por favor, intenta de nuevo más tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchIndependientes();
  }, []);

  // Filtrar independientes que no estén disponibles
  const independientesDisponibles = independientes.filter(independiente => {
    // Verificar disponibilidad en diferentes formatos posibles
    if (independiente.disponibilidad === 'No disponible') return false;
    if (independiente.disponible === false) return false;

    // Si llegamos aquí, la independiente está disponible
    return true;
  });

  // Ordenar independientes: destacadas primero, luego VIPs, luego el resto
  const independientesFiltrados = [...independientesDisponibles].sort((a, b) => {
    const aDestacada = a.destacada === true;
    const bDestacada = b.destacada === true;
    const aVIP = a.vip === true;
    const bVIP = b.vip === true;

    if (aDestacada && !bDestacada) return -1;
    if (!aDestacada && bDestacada) return 1;
    if (aVIP && !bVIP) return -1;
    if (!aVIP && bVIP) return 1;
    return 0;
  });

  return (
    <AppLayout>
      <Layout>
        {/* Título principal */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white inline-flex items-center">
            <span className="mr-3">💎</span>
            Independientes
            <span className="ml-3">💎</span>
          </h1>
          <p className="text-xl font-medium text-white mt-4">
            Descubre chicas independientes listas para brindarte una experiencia única 😘
          </p>
        </div>

        {/* Estado de carga */}
        {loading ? (
          <div className="flex flex-col justify-center items-center h-64">
            <div className="w-16 h-16 relative animate-pulse-glow">
              <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#3f51b5] animate-spin"></div>
              <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#00bcd4] animate-spin animate-reverse"></div>
            </div>
            <p className="mt-4 text-gray-400">Cargando independientes...</p>
          </div>
        ) : error ? (
          <div className="bg-[#2d0a16] p-6 rounded-lg border border-red-800">
            <h3 className="text-xl font-semibold text-red-400 mb-2">Error</h3>
            <p className="text-red-300">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-red-800 hover:bg-red-700 text-white rounded-md transition-colors"
            >
              Intentar nuevamente
            </button>
          </div>
        ) : independientesFiltrados.length === 0 ? (
          <div className="text-center py-16 bg-[#1e1e1e] rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <p className="text-gray-400 text-lg">No hay independientes disponibles en este momento.</p>
          </div>
        ) : (
          <>
            <h2 id="independientes-section" className="text-2xl font-bold text-white mb-3 flex items-center">
              <span className="mr-2">💎</span>
              Disponibles
              <span className="ml-3 text-sm font-normal bg-[#3f51b5] bg-opacity-50 px-2 py-1 rounded-full">
                {independientesFiltrados.length}
              </span>
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-16">
              {independientesFiltrados.map(independiente => (
                <IndependienteCard key={independiente.id} modelo={independiente} />
              ))}
            </div>
          </>
        )}
      </Layout>
    </AppLayout>
  );
};

export default IndependientesPage;
