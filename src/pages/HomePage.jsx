import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { modelosService } from '../services/pocketbase';
import BrujitaCard from '../components/BrujitaCard';
import Layout from '../components/Layout';
import BannerCarousel from '../components/BannerCarousel';
import logger from '../services/logService';
import AppLayout from '../components/AppLayout';

const HomePage = () => {
  const [modelos, setModelos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchModelos = async () => {
      try {
        setLoading(true);
        const response = await modelosService.getAll();
        logger.debug('Datos recibidos de la API:', response.items);
        setModelos(response.items);
      } catch (err) {
        logger.error('Error al cargar modelos:', err);
        setError('No se pudieron cargar los modelos. Por favor, intenta de nuevo más tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchModelos();
  }, []);


  // Función para verificar si un modelo es VIP
  const isModeloVIP = (modelo) => {
    if (!modelo) return false;

    // Verificar si hay un campo directo
    if (modelo.vip === true || modelo.isVIP === true) return true;

    // Verificar en el campo estado
    if (modelo.estado) {
      if (typeof modelo.estado === 'string') {
        return modelo.estado === 'VIP' || modelo.estado.includes('VIP');
      } else if (Array.isArray(modelo.estado)) {
        return modelo.estado.includes('VIP');
      }
    }

    return false;
  };

  // Función para verificar si un modelo es Destacada
  const isModeloDestacada = (modelo) => {
    if (!modelo) return false;

    // Verificar si hay un campo directo
    if (modelo.destacada === true || modelo.isDestacada === true) return true;

    // Verificar en el campo estado
    if (modelo.estado) {
      if (typeof modelo.estado === 'string') {
        return modelo.estado === 'Destacada' || modelo.estado.includes('Destacada');
      } else if (Array.isArray(modelo.estado)) {
        return modelo.estado.includes('Destacada');
      }
    }

    return false;
  };

  // Ordenar modelos: destacadas primero, luego VIPs, luego el resto
  const modelosFiltrados = [...modelos].sort((a, b) => {
    const aDestacada = isModeloDestacada(a);
    const bDestacada = isModeloDestacada(b);
    const aVIP = isModeloVIP(a);
    const bVIP = isModeloVIP(b);

    if (aDestacada && !bDestacada) return -1;
    if (!aDestacada && bDestacada) return 1;
    if (aVIP && !bVIP) return -1;
    if (!aVIP && bVIP) return 1;
    return 0;
  });

  return (
    <AppLayout>
    <Layout>
      {/* Banner Principal - Carrusel */}
      <BannerCarousel />

      {/* Menú de navegación */}
      <div className="mb-8 grid grid-cols-2 md:grid-cols-2 lg:grid-cols-5 gap-3">
        <Link
          to="/bienvenida"
          className="flex items-center justify-center px-5 py-3 rounded-lg text-sm font-medium transition-all bg-[#9c27b0]/30 text-white border border-[#9c27b0] hover:bg-[#9c27b0]/50 hover:shadow-lg hover:shadow-[#9c27b0]/20 hover:translate-y-[-2px] active:translate-y-0"
        >
          <img src="img/bienvenida.svg" alt="Bienvenida" className="h-6 w-auto mr-2" />
          <span>Bienvenida</span>
        </Link>
        <Link
          to="/ofertas"
          className="flex items-center justify-center px-5 py-3 rounded-lg text-sm font-medium transition-all bg-[#e91e63]/30 text-white border border-[#e91e63] hover:bg-[#e91e63]/50 hover:shadow-lg hover:shadow-[#e91e63]/20 hover:translate-y-[-2px] active:translate-y-0"
        >
          <img src="img/ofertas.svg" alt="Ofertas" className="h-6 w-auto mr-2" />
          <span>Ofertas</span>
        </Link>
        <Link
          to="/clientesvip"
          className="flex items-center justify-center px-5 py-3 rounded-lg text-sm font-medium transition-all bg-[#ffc107]/30 text-white border border-[#ffc107] hover:bg-[#ffc107]/50 hover:shadow-lg hover:shadow-[#ffc107]/20 hover:translate-y-[-2px] active:translate-y-0"
        >
          <img src="img/vip.svg" alt="Clientes VIP" className="h-6 w-auto mr-2" />
          <span>Clientes VIP</span>
        </Link>
        <Link
          to="/independientes"
          className="flex items-center justify-center px-5 py-3 rounded-lg text-sm font-medium transition-all bg-[#3f51b5]/30 text-white border border-[#3f51b5] hover:bg-[#3f51b5]/50 hover:shadow-lg hover:shadow-[#3f51b5]/20 hover:translate-y-[-2px] active:translate-y-0"
        >
          <img src="img/independientes.svg" alt="Independientes" className="h-6 w-auto mr-2" />
          <span>Independientes</span>
        </Link>
        <a
          href="https://t.me/ReferenciasBSMx"
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center justify-center px-5 py-3 rounded-lg text-sm font-medium transition-all bg-[#00bcd4]/30 text-white border border-[#00bcd4] hover:bg-[#00bcd4]/50 hover:shadow-lg hover:shadow-[#00bcd4]/20 hover:translate-y-[-2px] active:translate-y-0 col-span-2 md:col-span-2 lg:col-span-1"
        >
          <img src="img/referencias.svg" alt="Referencias" className="h-6 w-auto mr-2" />
          <span>Referencias</span>
        </a>
      </div>

      {/* Contenido principal */}

      {/* Estado de carga */}
      {loading ? (
        <div className="flex flex-col justify-center items-center h-64">
          <div className="w-16 h-16 relative animate-pulse-glow">
            <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#9c27b0] animate-spin"></div>
            <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#e91e63] animate-spin animate-reverse"></div>
          </div>
          <p className="mt-4 text-gray-400">Cargando brujitas...</p>
        </div>
      ) : error ? (
        <div className="bg-[#2d0a16] p-6 rounded-lg border border-red-800">
          <h3 className="text-xl font-semibold text-red-400 mb-2">Error</h3>
          <p className="text-red-300">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-800 hover:bg-red-700 text-white rounded-md transition-colors"
          >
            Intentar nuevamente
          </button>
        </div>
      ) : modelosFiltrados.length === 0 ? (
        <div className="text-center py-16 bg-[#1e1e1e] rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <p className="text-gray-400 text-lg">No se encontraron brujitas disponibles.</p>
        </div>
      ) : (
        <>
          <h2 id="brujitas-section" className="text-2xl font-bold text-white mb-3 flex items-center">
            <span className="mr-2">✨</span>
            Brujitas Disponibles
            <span className="ml-3 text-sm font-normal bg-[#6a0dad] bg-opacity-50 px-2 py-1 rounded-full">
              {modelosFiltrados.length}
            </span>
          </h2>

          {/* Texto descriptivo debajo del título */}
          <div className="text-left mb-6">
            <p className="text-xl font-medium text-white">
              Nuestras encantadoras brujitas están listas para llevarte a un mundo de placer 😈
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-16">
            {modelosFiltrados.map(modelo => (
              <BrujitaCard key={modelo.id} modelo={modelo} />
            ))}
          </div>


        </>
      )}
    </Layout>
    </AppLayout>
  );
};

export default HomePage;
