import { useState, useEffect } from 'react';
import { ofertasService } from '../services/ofertasService';
import Layout from '../components/Layout';
import AppLayout from '../components/AppLayout';
import DiasBrujitasOfertasGrid from '../components/OfertasPageComponents/DiasBrujitasOfertasGrid';
import OfertaAccordion from '../components/OfertasPageComponents/OfertaAccordion';

const OfertasPage = () => {
  const [ofertas, setOfertas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchOfertas = async () => {
      try {
        setLoading(true);
        // Solo obtener ofertas visibles para la página pública
        const response = await ofertasService.getAll({
          visible: true
        });

        // Asegurarse de que tenemos datos válidos
        if (response && response.items && Array.isArray(response.items)) {
          setOfertas(response.items);
        } else {
          setOfertas([]);
        }
      } catch (err) {
        console.error('Error al cargar ofertas:', err);
        setError('No se pudieron cargar las ofertas. Por favor, intenta de nuevo más tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchOfertas();
  }, []);

  return (
    <AppLayout>
      <Layout>
        {/* Título principal */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white inline-flex items-center">
            <span className="mr-3">🔥</span>
            Ofertas Especiales
            <span className="ml-3">🔥</span>
          </h1>
          <p className="text-xl font-medium text-white mt-4">
            Las brujitas tienen estas ofertas para ti, para que te animes a pasar un momento con ellas 😘
          </p>
        </div>

        {/* Contenido principal */}
        <div className="max-w-6xl mx-auto">
          {loading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
            </div>
          ) : error ? (
            <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 text-center">
              <p className="text-red-400">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-2 px-4 py-2 bg-[#23233a] rounded-lg text-white hover:bg-[#29293d] transition-colors"
              >
                Reintentar
              </button>
            </div>
          ) : ofertas.length === 0 ? (
            <div className="bg-[#1e1e2e] rounded-xl p-8 text-center border border-[#3d3d3d]">
              <h2 className="text-xl font-semibold text-[#e0b3ff] mb-3">No hay ofertas disponibles</h2>
              <p className="text-gray-300">
                Actualmente no tenemos ofertas especiales. ¡Vuelve pronto para descubrir nuestras promociones!
              </p>
            </div>
          ) : (
            <>
              {/* Listado de ofertas */}
              <div className="mb-12">
                <div className="space-y-4">
                  {ofertas.map((oferta, index) => (
                    <OfertaAccordion
                      key={oferta.id || index}
                      oferta={oferta}
                    />
                  ))}
                </div>
              </div>

              {/* Sección de días y brujitas */}
              <DiasBrujitasOfertasGrid ofertas={ofertas} />

              {/* Nota informativa sobre condiciones */}
              <div className="bg-[#1e1e2e] rounded-xl p-6 border border-[#3d3d3d] mb-16">
                <h3 className="text-xl font-semibold text-[#e0b3ff] mb-3 flex items-center">
                  <span className="mr-2">📝</span> Información importante
                </h3>
                <div className="space-y-3 text-gray-300">
                  <p>
                    <span className="text-pink-400 font-medium">•</span> Las ofertas son válidas únicamente durante el día y horario en que la brujita las ofrece.
                  </p>
                  <p>
                    <span className="text-pink-400 font-medium">•</span> Solo aplican para el tipo de servicio que se menciona.
                  </p>
                  <p>
                    <span className="text-pink-400 font-medium">•</span> Para acceder a estas promociones especiales, debes haber disfrutado previamente de al menos un servicio, realizado aportaciones o participado en nuestras rifas.
                  </p>
                  <p>
                    <span className="text-pink-400 font-medium">•</span> <span className="text-[#e0b3ff] font-medium">Clientes VIP:</span> Tienen el privilegio exclusivo de solicitar cada oferta una sola vez por brujita, en cualquier día de la semana.
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      </Layout>
    </AppLayout>
  );
};

export default OfertasPage;
