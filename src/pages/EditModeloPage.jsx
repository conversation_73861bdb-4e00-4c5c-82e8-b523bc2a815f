import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { modelosService } from '../services/pocketbase';
import AdminLayout from '../components/AdminLayout';
import ModeloForm from '../components/ModeloForm';
import logger from '../services/logService';

const EditModeloPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [modelo, setModelo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchModelo = async () => {
      try {
        setLoading(true);
        const data = await modelosService.getById(id);
        setModelo(data);
      } catch (err) {
        logger.error('Error al cargar el modelo:', err);
        setError('No se pudo cargar la información de la brujita. Por favor, intenta de nuevo más tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchModelo();
  }, [id]);

  const handleSubmit = () => {
    // Redirigir a la página de administración después de actualizar
    navigate('/admin');
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="bg-red-50 p-4 rounded-md">
          <p className="text-red-500">{error}</p>
          <button
            onClick={() => navigate('/admin')}
            className="mt-4 text-blue-600 hover:underline"
          >
            Volver a administración
          </button>
        </div>
      </AdminLayout>
    );
  }

  if (!modelo) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No se encontró la brujita solicitada.</p>
          <button
            onClick={() => navigate('/admin')}
            className="mt-4 text-blue-600 hover:underline"
          >
            Volver a administración
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#e0b3ff] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#9c27b0]">✨</span> Editar Brujita: <span className="text-white">{modelo.nombre}</span>
        </h1>
      </div>

      <ModeloForm modelo={modelo} onSubmit={handleSubmit} />
    </AdminLayout>
  );
};

export default EditModeloPage;
