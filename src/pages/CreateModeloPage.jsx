import { useNavigate } from 'react-router-dom';
import AdminLayout from '../components/AdminLayout';
import ModeloForm from '../components/ModeloForm';

const CreateModeloPage = () => {
  const navigate = useNavigate();

  const handleSubmit = () => {
    // Redirigir a la página de administración después de crear
    navigate('/admin');
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#e0b3ff] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#9c27b0]">✨</span>Nueva Brujita
        </h1>
      </div>

      <ModeloForm onSubmit={handleSubmit} />
    </AdminLayout>
  );
};

export default CreateModeloPage;
