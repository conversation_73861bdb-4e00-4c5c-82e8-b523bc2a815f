import { useNavigate } from 'react-router-dom';
import AdminLayout from '../components/AdminLayout';
import BannerForm from '../components/BannerForm';

const CreateBannerPage = () => {
  const navigate = useNavigate();

  const handleSubmit = () => {
    // Redirigir a la sección de banners después de crear
    navigate('/admin?tab=banners');
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#e0b3ff] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#9c27b0]">🖼️</span> Nuevo Banner
        </h1>
      </div>

      <BannerForm
        onSubmit={handleSubmit}
        onCancel={() => navigate('/admin?tab=banners')}
      />
    </AdminLayout>
  );
};

export default CreateBannerPage;
