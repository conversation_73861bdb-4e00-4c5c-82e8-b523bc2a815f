@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Poppins', system-ui, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color-scheme: dark;
}

html {
  background-color: #000000 !important;
}

body {
  min-height: 100vh;
  background-color: #000000 !important;
  color: #ffffff;
  background-image: none !important;
  background-attachment: fixed;
}

/* Estilos personalizados */
@layer components {
  /* Mantenemos la clase card-brujita por compatibilidad, pero ahora usamos estilos inline */
  .card-brujita {
    @apply bg-[#1e1e1e] rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-[0_0_15px_rgba(156,39,176,0.5)] border border-[#3d3d5a];
  }

  .btn-primary {
    @apply bg-[#6a0dad] hover:bg-[#9c27b0] text-white font-medium py-2 px-4 rounded-md transition-colors duration-300;
  }

  .btn-secondary {
    @apply bg-transparent border border-[#6a0dad] text-[#9c27b0] hover:bg-[#6a0dad] hover:text-white font-medium py-2 px-4 rounded-md transition-colors duration-300;
  }

  .btn-disabled {
    @apply bg-[#3d3d3d] text-gray-500 font-medium py-2 px-4 rounded-md cursor-not-allowed;
  }

  .badge-vip {
    @apply px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1;
    background: linear-gradient(90deg, rgba(255,215,0,0.3) 0%, rgba(255,215,0,0.1) 100%);
    color: #ffd700;
    border: 1px solid rgba(255,215,0,0.3);
    box-shadow: 0 0 10px rgba(255,215,0,0.2);
    text-shadow: 0 0 5px rgba(255,215,0,0.5);
  }

  .badge-destacado {
    @apply px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1;
    background: linear-gradient(90deg, rgba(255,64,129,0.3) 0%, rgba(255,64,129,0.1) 100%);
    color: #ff4081;
    border: 1px solid rgba(255,64,129,0.3);
    box-shadow: 0 0 10px rgba(255,64,129,0.2);
    text-shadow: 0 0 5px rgba(255,64,129,0.5);
  }

  .badge-disponible {
    @apply px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1;
    background: linear-gradient(90deg, rgba(76,175,80,0.3) 0%, rgba(46,125,50,0.2) 100%);
    color: #4CAF50;
    border: 1px solid rgba(76,175,80,0.3);
    box-shadow: 0 0 10px rgba(76,175,80,0.2);
    text-shadow: 0 0 5px rgba(76,175,80,0.5);
  }

  .badge-no-disponible {
    @apply px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1;
    background: linear-gradient(90deg, rgba(244,67,54,0.3) 0%, rgba(211,47,47,0.2) 100%);
    color: #F44336;
    border: 1px solid rgba(244,67,54,0.3);
    box-shadow: 0 0 10px rgba(244,67,54,0.2);
    text-shadow: 0 0 5px rgba(244,67,54,0.5);
  }

  .input-dark {
    @apply bg-[#2d2d2d] text-white border border-[#3d3d3d] rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#9c27b0] focus:border-transparent;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-custom {
    @apply py-6 md:py-10;
  }

  /* Animaciones */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-in-out;
  }

  .animate-pulse-glow {
    animation: pulseGlow 2s infinite;
  }

  .animate-modal-appear {
    animation: modalAppear 0.3s ease-out forwards;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }

  .animate-pulse-slow {
    animation: pulse 3s ease-in-out infinite;
  }
}

/* Keyframes para animaciones */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulseGlow {
  0% { box-shadow: 0 0 5px rgba(156, 39, 176, 0.5); }
  50% { box-shadow: 0 0 20px rgba(156, 39, 176, 0.8); }
  100% { box-shadow: 0 0 5px rgba(156, 39, 176, 0.5); }
}

@keyframes modalAppear {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes pulse {
  0% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
  100% { opacity: 0.6; transform: scale(1); }
}

/* Estilos para el carrusel */
.carousel-bg-1 {
  background: linear-gradient(135deg, #4a148c 0%, #880e4f 100%);
  background-size: cover;
  background-position: center;
}

.carousel-bg-2 {
  background: linear-gradient(135deg, #6a1b9a 0%, #ad1457 100%);
  background-size: cover;
  background-position: center;
}

.carousel-bg-3 {
  background: linear-gradient(135deg, #7b1fa2 0%, #c2185b 100%);
  background-size: cover;
  background-position: center;
}

/* Estilos para el logo con efecto hover */
.logo-link {
  position: relative;
}

.logo-normal {
  transition: opacity 0.3s ease-in-out;
}

.logo-hover {
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.3s ease-in-out;
}

.logo-link:hover .logo-normal {
  opacity: 0;
}

.logo-link:hover .logo-hover {
  opacity: 1;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
  background: #6a0dad;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9c27b0;
}

/* Scrollbar personalizada para el modal de términos */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #1a1a2e;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #9c27b0;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #e91e63;
}
