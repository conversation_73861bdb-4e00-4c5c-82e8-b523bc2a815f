// pb_hooks/main.js - Este código se ejecuta en tu servidor PocketBase

router.addRoute({
    method: "PATCH", // O "PUT" si tu cliente prefiere enviar PUT para la actualización
    path: "/api/collections/:collectionName/records/:recordId/incrementVisitas", // Una ruta personalizada clara y específica
    requireAuth: false, // ¡IMPORTANTE! NO requiere autenticación para que los usuarios no autenticados puedan usarla
    handler: (c) => {
        const collectionName = c.pathParams.collectionName;
        const recordId = c.pathParams.recordId;
        const isAuth = !!c.request.authRecord; // Verifica si hay un usuario autenticado

        // Asegúrate de que sea una de tus colecciones permitidas
        if (collectionName !== "modelos" && collectionName !== "independientes") {
            return c.json(403, { "message": "Acceso denegado a esta colección" });
        }

        // Permitir tanto usuarios autenticados como no autenticados
        // El control de spam se maneja en el frontend con localStorage
        // Este endpoint es seguro porque:
        // 1. Solo permite incrementar en +1 (no acepta valores arbitrarios)
        // 2. Solo funciona en colecciones específicas (modelos/independientes)
        // 3. El frontend controla la frecuencia de llamadas (10 minutos)

        try {
            // 1. Obtener el registro actual de la base de datos
            const record = $app.dao().findRecordById(collectionName, recordId);
            if (!record) {
                return c.json(404, { "message": "Registro no encontrado" });
            }

            // 2. Incrementar el campo 'visitas' siempre en +1 (seguro)
            const currentVisitas = record.get("visitas") || 0;
            record.set("visitas", currentVisitas + 1);

            // 3. Guardar el registro actualizado en la base de datos
            $app.dao().saveRecord(record);

            // 4. Responder exitosamente al cliente
            return c.json(200, { "message": "Visitas incrementadas exitosamente", "newVisitas": record.get("visitas") });

        } catch (e) {
            // Manejo de errores
            $app.logger().error("Error al incrementar visitas:", e);
            return c.json(500, { "message": "Error interno del servidor", "error": e.message });
        }
    }
});