import React, { createContext, useState, useEffect, useContext } from 'react';
import { configService } from '../services/configService';
import logger from '../services/logService';

// Create the context
const ConfigContext = createContext();

// Custom hook to use the config context
export const useConfig = () => {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
};

// Provider component
export const ConfigProvider = ({ children }) => {
  const [config, setConfig] = useState({
    telegram: 'https://t.me/BrujitasMx', // Default values
    wa: 'https://wa.me/+527205373745',
    tiktok: '',
    twitter: '',
    loading: true,
    error: null
  });

  // Fetch config data on component mount
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        // Intentar obtener la configuración existente
        const response = await configService.getConfig();

        if (response && response.items && response.items.length > 0) {
          const configData = response.items[0];
          setConfig(prev => ({
            ...prev,
            telegram: configData.telegram || prev.telegram,
            wa: configData.wa || prev.wa,
            tiktok: configData.tiktok || prev.tiktok,
            twitter: configData.twitter || prev.twitter,
            loading: false,
            error: null
          }));
        } else {
          // Si no hay configuración, crear una inicial
          try {
            const initialConfig = await configService.createInitialConfig();
            setConfig(prev => ({
              ...prev,
              telegram: initialConfig.telegram || prev.telegram,
              wa: initialConfig.wa || prev.wa,
              tiktok: initialConfig.tiktok || prev.tiktok,
              twitter: initialConfig.twitter || prev.twitter,
              loading: false,
              error: null
            }));
          } catch (createError) {
            logger.error('Error creating initial config:', createError);
            setConfig(prev => ({
              ...prev,
              loading: false
            }));
          }
        }
      } catch (error) {
        logger.error('Error fetching config:', error);
        setConfig(prev => ({
          ...prev,
          loading: false,
          error: 'Error al cargar la configuración'
        }));
      }
    };

    fetchConfig();
  }, []);

  // Refresh config data
  const refreshConfig = async () => {
    setConfig(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await configService.getConfig();

      if (response && response.items && response.items.length > 0) {
        const configData = response.items[0];
        setConfig(prev => ({
          ...prev,
          telegram: configData.telegram || prev.telegram,
          wa: configData.wa || prev.wa,
          tiktok: configData.tiktok || prev.tiktok,
          twitter: configData.twitter || prev.twitter,
          loading: false,
          error: null
        }));
      } else {
        // If no config found, keep defaults but mark as loaded
        setConfig(prev => ({
          ...prev,
          loading: false
        }));
      }
    } catch (error) {
      logger.error('Error refreshing config:', error);
      setConfig(prev => ({
        ...prev,
        loading: false,
        error: 'Error al actualizar la configuración'
      }));
    }
  };

  // Value to be provided to consumers
  const value = {
    ...config,
    refreshConfig
  };

  return (
    <ConfigContext.Provider value={value}>
      {children}
    </ConfigContext.Provider>
  );
};

export default ConfigContext;
