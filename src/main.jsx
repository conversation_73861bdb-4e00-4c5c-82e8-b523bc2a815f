// Importar y activar el silenciador de consola lo antes posible
import consoleSilencer from './utils/consoleSilencer';
// Inicializar inmediatamente para evitar cualquier mensaje de error
consoleSilencer.init();

import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import './index.css';
import router from './routes';
import { SECURITY_CONFIG, APP_CONFIG } from './config';
import logger from './services/logService';
import { preloadCriticalImages } from './utils/imagePreloader';
import { ConfigProvider } from './context/ConfigContext';

// Función para detectar si hay intentos de XSS
const detectXssAttempt = () => {
  // Verificar si hay parámetros sospechosos en la URL
  const url = window.location.href;
  const suspiciousPatterns = SECURITY_CONFIG.XSS_PATTERNS;

  const isAttempt = suspiciousPatterns.some(pattern => pattern.test(url));

  if (isAttempt) {
    // Solo registrar en desarrollo
    if (APP_CONFIG.IS_DEV) {
      logger.error('Posible intento de XSS detectado');
    }
    // Redirigir a la página principal
    window.location.href = '/';
    return true;
  }

  return false;
};

// Función para registrar información de inicio de la aplicación
const logAppStart = () => {
  // Solo registrar logs en desarrollo
  if (APP_CONFIG.IS_DEV) {
    logger.info(`Iniciando ${APP_CONFIG.APP_NAME} v${APP_CONFIG.VERSION}`);
    logger.info(`Modo: ${APP_CONFIG.IS_DEV ? 'Desarrollo' : 'Producción'}`);
  }

  // Precargar imágenes críticas
  preloadCriticalImages()
    .then(() => {
      if (APP_CONFIG.IS_DEV) {
        logger.info('Imágenes críticas precargadas correctamente');
      }
    })
    .catch(error => {
      if (APP_CONFIG.IS_DEV) {
        logger.error('Error al precargar imágenes críticas:', error);
      }
    });
};

// Verificar si hay intentos de XSS antes de renderizar la aplicación
if (!detectXssAttempt()) {
  // Registrar información de inicio
  logAppStart();

  // Renderizar la aplicación
  createRoot(document.getElementById('root')).render(
    <StrictMode>
      <ConfigProvider>
        <RouterProvider router={router} />
      </ConfigProvider>
    </StrictMode>,
  );
}
