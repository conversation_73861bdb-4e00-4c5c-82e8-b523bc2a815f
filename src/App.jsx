import { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';

// Componentes
import SimpleAgeVerification from './components/SimpleAgeVerification';

function App() {
  const [showAgeVerification, setShowAgeVerification] = useState(true);

  // Verificar si el usuario ya ha aceptado los términos
  useEffect(() => {
    const hasAccepted = localStorage.getItem('termsAccepted');
    if (hasAccepted === 'true') {
      setShowAgeVerification(false);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('termsAccepted', 'true');
    setShowAgeVerification(false);
  };

  const handleReject = () => {
    window.location.href = 'https://www.google.com';
  };

  return (
    <>
      {/* Modal de verificación de edad */}
      {showAgeVerification && (
        <SimpleAgeVerification
          onAccept={handleAccept}
          onReject={handleReject}
        />
      )}

      {/* Renderizar las rutas anidadas */}
      <Outlet />
    </>
  );
}

export default App
