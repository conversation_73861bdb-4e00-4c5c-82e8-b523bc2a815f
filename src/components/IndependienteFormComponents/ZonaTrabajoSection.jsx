import React, { useState } from 'react';

const ZonaTrabajoSection = ({ formData, setFormData }) => {
  const [nuevaZona, setNuevaZona] = useState('');

  // Función para agregar una nueva zona
  const agregarZona = () => {
    if (nuevaZona.trim() === '') return;

    // Verificar si la zona ya existe
    if (formData.zonas && formData.zonas.includes(nuevaZona.trim())) {
      return;
    }

    // Agregar la nueva zona
    setFormData(prev => ({
      ...prev,
      zonas: [...(prev.zonas || []), nuevaZona.trim()]
    }));

    // Limpiar el input
    setNuevaZona('');
  };

  // Función para eliminar una zona
  const eliminarZona = (index) => {
    setFormData(prev => ({
      ...prev,
      zonas: prev.zonas.filter((_, i) => i !== index)
    }));
  };

  // <PERSON>ejar la tecla Enter para agregar una zona
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      agregarZona();
    }
  };

  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
        <span className="text-[#9c27b0]">📍</span> Zonas de Trabajo
      </h3>
      <div className="bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner">
        <div className="flex items-center gap-2 mb-4">
          <input
            type="text"
            value={nuevaZona}
            onChange={(e) => setNuevaZona(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1 p-3 border rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#181828] text-white border-[#29293d]"
            placeholder="Ej: Tultitlan"
          />
          <button
            type="button"
            onClick={agregarZona}
            className="px-4 py-3 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-lg hover:shadow-lg transition-all"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
          </button>
        </div>

        {/* Mostrar las zonas agregadas */}
        <div className="flex flex-wrap gap-2 mt-3">
          {formData.zonas && formData.zonas.length > 0 ? (
            formData.zonas.map((zona, index) => (
              <div
                key={index}
                className="flex items-center bg-[#29293d] px-3 py-1.5 rounded-full text-sm text-white"
              >
                <span className="mr-1">📍</span>
                {zona}
                <button
                  type="button"
                  onClick={() => eliminarZona(index)}
                  className="ml-2 text-gray-400 hover:text-red-400"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            ))
          ) : (
            <p className="text-gray-400 text-sm italic">No hay zonas de trabajo agregadas</p>
          )}
        </div>

        <p className="text-xs text-gray-400 mt-4">
          Agrega las zonas donde la independiente ofrece sus servicios. Presiona Enter o el botón + para agregar cada zona.
        </p>
      </div>
    </div>
  );
};

export default ZonaTrabajoSection;
