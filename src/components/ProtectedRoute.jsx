import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/pocketbase';

/**
 * Componente para proteger rutas que requieren autenticación
 * Redirige al usuario a la página de login si no está autenticado
 */
const ProtectedRoute = ({ children }) => {
  const navigate = useNavigate();
  
  useEffect(() => {
    // Verificar si el usuario está autenticado
    if (!authService.isAuthenticated()) {
      // Redirigir al login si no está autenticado
      navigate('/login', { replace: true });
    }
  }, [navigate]);

  // Si el usuario está autenticado, renderizar los hijos
  return authService.isAuthenticated() ? children : null;
};

export default ProtectedRoute;
