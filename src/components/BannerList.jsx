import { useState } from 'react';
import { bannerService } from '../services/bannerService';
import logger from '../services/logService';

const BannerList = ({ banners, onEdit, onDelete, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Función para cambiar el orden de un banner
  const handleOrderChange = async (bannerId, newOrder) => {
    try {
      setLoading(true);
      setError(null);
      await bannerService.updateOrder(bannerId, newOrder);
      onRefresh(); // Refrescar la lista después de cambiar el orden
    } catch (err) {
      logger.error('Error al cambiar el orden:', err);
      setError('No se pudo cambiar el orden. Por favor, intenta de nuevo más tarde.');
    } finally {
      setLoading(false);
    }
  };

  // Función para mover un banner hacia arriba (disminuir orden)
  const moveUp = (banner, index) => {
    if (index === 0) return; // Ya está en la primera posición
    const prevBanner = banners[index - 1];
    handleOrderChange(banner.id, prevBanner.orden);
    handleOrderChange(prevBanner.id, banner.orden);
  };

  // Función para mover un banner hacia abajo (aumentar orden)
  const moveDown = (banner, index) => {
    if (index === banners.length - 1) return; // Ya está en la última posición
    const nextBanner = banners[index + 1];
    handleOrderChange(banner.id, nextBanner.orden);
    handleOrderChange(nextBanner.id, banner.orden);
  };

  // Si no hay banners, mostrar mensaje
  if (banners.length === 0) {
    return (
      <div className="bg-[#181828] p-8 rounded-lg shadow-lg text-center border border-[#29293d]">
        <p className="text-gray-300 mb-4">No hay banners registrados todavía.</p>
      </div>
    );
  }

  return (
    <div className="bg-[#181828] rounded-lg shadow-lg border border-[#29293d] overflow-hidden">
      {error && (
        <div className="bg-[#2d0a16] p-4 m-4 rounded-lg border border-red-800">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-[#23233a] border-b border-[#29293d]">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Imagen</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Título</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Estado</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">Posición</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-[#29293d]">
            {banners.map((banner, index) => (
              <tr key={banner.id} className="bg-[#1e1e2e] hover:bg-[#23233a] transition-colors">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex-shrink-0 h-12 w-20">
                    <img
                      className="h-12 w-20 object-cover rounded"
                      src={banner.imagen ? bannerService.getImageUrl(banner, banner.imagen) : '/img/placeholder.png'}
                      alt={banner.titulo}
                    />
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm font-medium text-white">{banner.titulo}</div>
                  {banner.descripcion && (
                    <div className="text-xs text-gray-400 mt-1 line-clamp-2">{banner.descripcion}</div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${banner.activo ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {banner.activo ? 'Activo' : 'Inactivo'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                  <div className="flex items-center justify-center space-x-3">
                    <button
                      onClick={() => moveUp(banner, index)}
                      disabled={index === 0 || loading}
                      className={`text-gray-400 hover:text-white p-2 rounded-full hover:bg-[#29293d] transition-colors ${index === 0 || loading ? 'opacity-50 cursor-not-allowed' : ''}`}
                      title="Mover hacia arriba"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                      </svg>
                    </button>
                    <button
                      onClick={() => moveDown(banner, index)}
                      disabled={index === banners.length - 1 || loading}
                      className={`text-gray-400 hover:text-white p-2 rounded-full hover:bg-[#29293d] transition-colors ${index === banners.length - 1 || loading ? 'opacity-50 cursor-not-allowed' : ''}`}
                      title="Mover hacia abajo"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => onEdit(banner)}
                      className="text-blue-400 hover:text-blue-300 transition-colors"
                      disabled={loading}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => onDelete(banner)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                      disabled={loading}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default BannerList;
