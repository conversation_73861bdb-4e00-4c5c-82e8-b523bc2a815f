import { useState, useEffect, useCallback } from 'react';
import { modelosService } from '../services/pocketbase';
import { independientesService } from '../services/independientesService';
import logger from '../services/logService';
import LazyImage from './LazyImage';

const GaleriaModal = ({ isOpen, onClose, brujita, initialImageIndex = 0 }) => {
  const [currentIndex, setCurrentIndex] = useState(initialImageIndex);
  const [isLoading, setIsLoading] = useState(true);
  const [images, setImages] = useState([]);
  const [error, setError] = useState(false);

  // Manejar teclas de navegación (usando useCallback para evitar recreaciones)
  // IMPORTANTE: Los hooks deben estar en el mismo orden en cada renderizado
  const handleKeyDown = useCallback((e) => {
    if (!isOpen) return;

    if (e.key === 'ArrowLeft') {
      setIsLoading(true);
      setCurrentIndex((prevIndex) =>
        prevIndex === 0 ? (images.length > 0 ? images.length - 1 : 0) : prevIndex - 1
      );
    } else if (e.key === 'ArrowRight') {
      setIsLoading(true);
      setCurrentIndex((prevIndex) =>
        prevIndex === (images.length - 1) ? 0 : prevIndex + 1
      );
    } else if (e.key === 'Escape') {
      onClose();
    }
  }, [isOpen, onClose, images.length]);

  // Añadir event listener para teclas
  useEffect(() => {
    if (isOpen) {
      window.addEventListener('keydown', handleKeyDown);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isOpen, handleKeyDown]);

  // Preparar las imágenes cuando se abre el modal
  useEffect(() => {
    if (isOpen && brujita) {
      try {
        // Resetear estados
        setIsLoading(true);
        setError(false);

        // Preparar las imágenes
        const galeriaArray = Array.isArray(brujita.galeria) ? brujita.galeria : [brujita.galeria];

        // Filtrar imágenes vacías
        const validImages = galeriaArray.filter(img => img);

        // Determinar qué servicio usar basado en la colección del modelo
        const service = brujita.collectionId === 'independientes' || brujita.collectionName === 'independientes'
          ? independientesService
          : modelosService;

        // Generar URLs
        const imageUrls = validImages.map(img => ({
          id: img,
          url: service.getImageUrl(brujita, img)
        }));

        if (imageUrls.length > 0) {
          setImages(imageUrls);
          setCurrentIndex(initialImageIndex < imageUrls.length ? initialImageIndex : 0);
        } else {
          setImages([]);
          setError(true);
        }
      } catch (err) {
        logger.error('Error al cargar imágenes:', err);
        setError(true);
      } finally {
        setIsLoading(false);
      }
    }
  }, [isOpen, brujita, initialImageIndex]);

  // Si el modal no está abierto, no renderizar nada
  if (!isOpen) return null;

  // Navegar a la imagen anterior
  const prevImage = () => {
    if (images.length <= 1) return;

    setIsLoading(true);
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  // Navegar a la imagen siguiente
  const nextImage = () => {
    if (images.length <= 1) return;

    setIsLoading(true);
    setCurrentIndex((prevIndex) =>
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };

  // Si hay un error o no hay imágenes, mostrar mensaje
  if (error || !images || images.length === 0) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90">
        <div className="text-white text-center p-8 bg-[#1e1e1e] rounded-lg max-w-md">
          <h3 className="text-xl font-semibold mb-4">No hay imágenes disponibles</h3>
          <p className="mb-6 text-gray-300">No se pudieron cargar las imágenes.</p>
          <button
            className="btn-primary w-full"
            onClick={onClose}
          >
            Cerrar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
      onClick={onClose}
    >
      <div
        className="relative max-w-4xl max-h-[90vh] w-full mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Imagen actual */}
        <div className="relative flex justify-center items-center min-h-[50vh] max-h-[80vh] overflow-hidden">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-10">
              <div className="w-12 h-12 border-4 border-t-4 border-[#9c27b0] rounded-full animate-spin"></div>
            </div>
          )}

          {images[currentIndex] && (
            <LazyImage
              src={images[currentIndex].url}
              alt={`Imagen ${currentIndex + 1} de ${brujita.nombre || 'la modelo'}`}
              className="max-h-[80vh] max-w-full w-auto h-auto mx-auto object-contain rounded-lg bg-black"
              objectFit="contain"
              onLoad={() => setIsLoading(false)}
              onError={(e) => {
                setIsLoading(false);
                e.target.src = '/img/placeholder.png';
              }}
            />
          )}
        </div>

        {/* Controles de navegación */}
        <div className="absolute inset-x-0 bottom-0 flex justify-between items-center p-4 bg-black bg-opacity-50">
          <div className="text-white text-sm font-medium">
            {currentIndex + 1} / {images.length}
          </div>

          <div className="flex space-x-4">
            <button
              className="p-2 rounded-full bg-[#1e1e1e] bg-opacity-70 text-white hover:bg-opacity-100 transition-all"
              onClick={prevImage}
              disabled={images.length <= 1}
              aria-label="Imagen anterior"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button
              className="p-2 rounded-full bg-[#1e1e1e] bg-opacity-70 text-white hover:bg-opacity-100 transition-all"
              onClick={nextImage}
              disabled={images.length <= 1}
              aria-label="Imagen siguiente"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Botón de cierre */}
        <button
          className="absolute top-4 right-4 p-2 rounded-full bg-[#1e1e1e] bg-opacity-70 text-white hover:bg-opacity-100 transition-all"
          onClick={onClose}
          aria-label="Cerrar galería"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default GaleriaModal;
