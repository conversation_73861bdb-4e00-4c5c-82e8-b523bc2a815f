import { useState, useEffect } from 'react';

const SeccionesDescripcion = ({ secciones, onChange }) => {
  const [seccionesList, setSeccionesList] = useState([]);

  // Inicializar las secciones cuando cambia el prop
  useEffect(() => {
    let seccionesData = [];

    // Si hay secciones proporcionadas
    if (secciones) {
      // Si es un string, intentar parsearlo como JSON
      if (typeof secciones === 'string') {
        try {
          const parsed = JSON.parse(secciones);
          // Verificar si el resultado es un array
          if (Array.isArray(parsed)) {
            seccionesData = parsed;
          } else {
            // Si no es un array, crear una sección con el contenido
            seccionesData = [{ titulo: 'Descripción', descripcion: secciones }];
          }
        } catch (error) {
          console.warn('Error al parsear secciones:', error);
          // Si hay error al parsear, usar el string como descripción
          seccionesData = [{ titulo: 'Descripción', descripcion: secciones }];
        }
      } 
      // Si es un array, usarlo directamente
      else if (Array.isArray(secciones)) {
        seccionesData = secciones;
      }
      // Si no es string ni array, crear una sección vacía
      else {
        seccionesData = [{ titulo: '', descripcion: '' }];
      }
    } 
    // Si no hay secciones, inicializar con una sección vacía
    else {
      seccionesData = [{ titulo: '', descripcion: '' }];
    }

    // Asegurarse de que cada sección tenga la estructura correcta
    const seccionesFormateadas = seccionesData.map(seccion => ({
      titulo: seccion?.titulo || '',
      descripcion: seccion?.descripcion || ''
    }));
    
    setSeccionesList(seccionesFormateadas);
  }, [secciones]);

  // Manejar cambios en los campos de una sección
  const handleSeccionChange = (index, field, value) => {
    const updatedSecciones = [...seccionesList];
    updatedSecciones[index] = {
      ...updatedSecciones[index],
      [field]: value
    };

    setSeccionesList(updatedSecciones);
    onChange(updatedSecciones);
  };

  // Agregar una nueva sección
  const addSeccion = () => {
    const updatedSecciones = [...seccionesList, { titulo: '', descripcion: '' }];
    setSeccionesList(updatedSecciones);
    onChange(updatedSecciones);
  };

  // Eliminar una sección
  const removeSeccion = (index) => {
    if (seccionesList.length <= 1) {
      // Siempre mantener al menos una sección
      return;
    }

    const updatedSecciones = seccionesList.filter((_, i) => i !== index);
    setSeccionesList(updatedSecciones);
    onChange(updatedSecciones);
  };

  // Mover una sección hacia arriba
  const moveSeccionUp = (index) => {
    if (index === 0) return; // Ya está en la primera posición

    const updatedSecciones = [...seccionesList];
    const temp = updatedSecciones[index];
    updatedSecciones[index] = updatedSecciones[index - 1];
    updatedSecciones[index - 1] = temp;

    setSeccionesList(updatedSecciones);
    onChange(updatedSecciones);
  };

  // Mover una sección hacia abajo
  const moveSeccionDown = (index) => {
    if (index === seccionesList.length - 1) return; // Ya está en la última posición

    const updatedSecciones = [...seccionesList];
    const temp = updatedSecciones[index];
    updatedSecciones[index] = updatedSecciones[index + 1];
    updatedSecciones[index + 1] = temp;

    setSeccionesList(updatedSecciones);
    onChange(updatedSecciones);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="text-md font-semibold text-[#e0b3ff]">Secciones de la Oferta</h4>
        <button
          type="button"
          onClick={addSeccion}
          className="px-3 py-1 bg-[#9c27b0] text-white rounded-md hover:bg-[#7b1fa2] transition-colors text-sm flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Agregar Sección
        </button>
      </div>

      {seccionesList.map((seccion, index) => (
        <div key={index} className="bg-[#1e1e2e] p-4 rounded-lg border border-[#3d3d3d] relative">
          <div className="absolute right-2 top-2 flex space-x-1">
            <button
              type="button"
              onClick={() => moveSeccionUp(index)}
              disabled={index === 0}
              className={`p-1 rounded-md ${index === 0 ? 'text-gray-500 cursor-not-allowed' : 'text-gray-300 hover:bg-[#3d3d5a] hover:text-white'}`}
              title="Mover arriba"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
            </button>
            <button
              type="button"
              onClick={() => moveSeccionDown(index)}
              disabled={index === seccionesList.length - 1}
              className={`p-1 rounded-md ${index === seccionesList.length - 1 ? 'text-gray-500 cursor-not-allowed' : 'text-gray-300 hover:bg-[#3d3d5a] hover:text-white'}`}
              title="Mover abajo"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
            <button
              type="button"
              onClick={() => removeSeccion(index)}
              disabled={seccionesList.length <= 1}
              className={`p-1 rounded-md ${seccionesList.length <= 1 ? 'text-gray-500 cursor-not-allowed' : 'text-red-400 hover:bg-red-900/30 hover:text-red-300'}`}
              title="Eliminar sección"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </button>
          </div>

          <div className="space-y-3 mt-2">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Título de la sección <span className="text-pink-400">*</span>
              </label>
              <input
                type="text"
                value={seccion.titulo}
                onChange={(e) => handleSeccionChange(index, 'titulo', e.target.value)}
                className="w-full p-2 border border-[#3d3d5a] rounded-md bg-[#23233a] text-white text-sm"
                placeholder="Ej: Condiciones"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Contenido <span className="text-pink-400">*</span>
              </label>
              <textarea
                value={seccion.descripcion}
                onChange={(e) => handleSeccionChange(index, 'descripcion', e.target.value)}
                rows="3"
                className="w-full p-2 border border-[#3d3d5a] rounded-md bg-[#23233a] text-white text-sm"
                placeholder="Describe esta sección..."
              ></textarea>
            </div>
          </div>
        </div>
      ))}

      <p className="text-xs text-gray-400 mt-1">
        Puedes agregar múltiples secciones para organizar mejor la información de la oferta.
        Cada sección tendrá su propio título y contenido.
      </p>
    </div>
  );
};

export default SeccionesDescripcion;
