import { Link } from 'react-router-dom';
import { independientesService } from '../services/independientesService';
import { useState, useEffect } from 'react';
import logger from '../services/logService';
import LazyImage from './LazyImage';

const IndependienteCard = ({ modelo }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Renombrar la variable para mayor claridad
  const independiente = modelo;

  // Obtener los campos de la independiente con fallbacks para diferentes nombres de campo
  const nombre = independiente.nombre || independiente.name || 'Sin nombre';
  const edad = independiente.edad || independiente.age || '';
  const perfilCorporal = independiente.perfil_corporal || independiente.profile || '';

  // Verificar si la independiente es VIP o Destacada
  let isVIP = false;
  let isDestacada = false;

  // Verificar campos booleanos directos
  if (independiente.vip === true) isVIP = true;
  if (independiente.destacada === true) isDestacada = true;

  // Verificar en el campo estado (puede ser string o array)
  const estado = independiente.estado || [];
  if (typeof estado === 'string') {
    isVIP = isVIP || estado === 'VIP' || estado.includes('VIP');
    isDestacada = isDestacada || estado === 'Destacada' || estado.includes('Destacada');
  } else if (Array.isArray(estado)) {
    isVIP = isVIP || estado.includes('VIP');
    isDestacada = isDestacada || estado.includes('Destacada');
  } else if (typeof estado === 'object' && estado !== null) {
    // Si es un objeto, verificar si tiene propiedades VIP o Destacada
    isVIP = isVIP || estado.VIP || estado.vip || false;
    isDestacada = isDestacada || estado.Destacada || estado.destacada || false;
  }

  // Solo registrar en modo debug (no visible en producción)
  logger.debug(`Independiente ${nombre} (${independiente.id}):`, {
    isVIP,
    isDestacada,
    estado
  });

  // Procesar zonas de trabajo
  const zonas = (() => {
    try {
      if (independiente.zonas) {
        if (typeof independiente.zonas === 'string') {
          return JSON.parse(independiente.zonas);
        }
        if (Array.isArray(independiente.zonas)) {
          return independiente.zonas;
        }
      }
      return [];
    } catch (error) {
      logger.error('Error al parsear zonas:', error);
      return [];
    }
  })();

  // Buscar la imagen de perfil
  const imagenPerfil = independiente.imagen_perfil || '';

  // Obtener la URL de la imagen de perfil
  const imageUrl = imagenPerfil
    ? independientesService.getImageUrl(independiente, imagenPerfil)
    : '/img/placeholder.png'; // Imagen de placeholder

  // Efecto para animar la entrada de la tarjeta
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, Math.random() * 300); // Efecto escalonado

    return () => clearTimeout(timer);
  }, []);

  return (
    <Link
      to={`/independiente/${independiente.id}`}
      className={`block transform transition-all duration-500 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="card-independiente group relative rounded-xl overflow-hidden shadow-lg border border-[#3d3d5a]">
        {/* Overlay de gradiente */}
        <div className="absolute inset-0 bg-gradient-to-t from-[#000000] via-transparent to-transparent opacity-70 z-10"></div>

        {/* Imagen con LazyImage */}
        <div className="h-80 overflow-hidden">
          <LazyImage
            src={imageUrl}
            alt={`Independiente ${nombre}`}
            className={`w-full h-full transition-transform duration-700 ${isHovered ? 'scale-110' : 'scale-100'}`}
            objectFit="cover"
            onError={(e) => {
              e.target.src = '/img/placeholder.png';
            }}
          />
        </div>

        {/* Eliminamos los badges de la parte superior para evitar duplicación */}

        {/* Información */}
        <div className="absolute bottom-0 left-0 right-0 p-4 z-20">
          <h3 className="text-xl font-bold text-white mb-1">{nombre}</h3>
          {/* Si tiene ambos tags (VIP y destacada), mostrarlos en líneas separadas */}
          {isVIP && isDestacada ? (
            <>
              {/* Primera línea: edad y perfil */}
              <div className="flex flex-wrap items-center gap-2 mb-1">
                {edad && (
                  <span className="bg-black bg-opacity-50 px-2 py-1 rounded-full text-xs text-white">
                    {edad} años
                  </span>
                )}
                {perfilCorporal && (
                  <span className="bg-[#2d2d2d] px-2 py-1 rounded-full text-xs text-gray-200">
                    {perfilCorporal}
                  </span>
                )}
              </div>
              {/* Segunda línea: VIP y destacada */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="badge-vip">
                  <span>⭐</span> VIP
                </span>
                <span className="badge-destacado">
                  <span>🔥</span> Destacada
                </span>
              </div>
            </>
          ) : (
            /* Si solo tiene un tag o ninguno, mostrar todo en una línea pero con orden: primero edad y perfil, luego tags */
            <div className="flex flex-wrap items-center gap-2">
              {/* Primero edad y perfil */}
              {edad && (
                <span className="bg-black bg-opacity-50 px-2 py-1 rounded-full text-xs text-white">
                  {edad} años
                </span>
              )}
              {perfilCorporal && (
                <span className="bg-[#2d2d2d] px-2 py-1 rounded-full text-xs text-gray-200">
                  {perfilCorporal}
                </span>
              )}
              {/* Después los tags de VIP o destacada */}
              {isVIP && (
                <span className="badge-vip">
                  <span>⭐</span> VIP
                </span>
              )}
              {isDestacada && (
                <span className="badge-destacado">
                  <span>🔥</span> Destacada
                </span>
              )}
            </div>
          )}

          {/* Zonas de trabajo */}
          {zonas.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {zonas.slice(0, 2).map((zona, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-[#3f51b5] bg-opacity-50 text-white"
                >
                  <span className="mr-1">📍</span>
                  {zona}
                </span>
              ))}
              {zonas.length > 2 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-[#3f51b5] bg-opacity-50 text-white">
                  +{zonas.length - 2}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Efecto de brillo en hover */}
        <div className={`absolute inset-0 transition-opacity duration-300 pointer-events-none ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
          <div className="absolute inset-0 bg-gradient-to-t from-[#3f51b5] to-transparent opacity-30"></div>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3f51b5] to-[#00bcd4]"></div>
          <div className="absolute inset-0 rounded-xl border-2 border-[#3f51b5] opacity-50"></div>
          <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-[#3f51b5] to-transparent opacity-20"></div>
        </div>
      </div>
    </Link>
  );
};

export default IndependienteCard;
