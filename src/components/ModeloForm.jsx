import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { modelosService } from '../services/pocketbase';
import logger from '../services/logService';
import BasicInfoSection from './ModeloFormComponents/BasicInfoSection';
import MeasurementsSection from './ModeloFormComponents/MeasurementsSection';
import PersonalInfoSection from './ModeloFormComponents/PersonalInfoSection';
import AvailabilitySection from './ModeloFormComponents/AvailabilitySection';
import OfertasDiasSection from './ModeloFormComponents/OfertasDiasSection';
import ImageUploadSection from './ModeloFormComponents/ImageUploadSection';
import FormActions from './ModeloFormComponents/FormActions';

const ModeloForm = ({ modelo = null, onSubmit }) => {
  const navigate = useNavigate();
  const isEditing = !!modelo;

  // Estado inicial del formulario
  const [formData, setFormData] = useState({
    nombre: '',
    edad: '',
    perfil_corporal: '',
    destacada: false,
    vip: false,
    disponibilidad: 'Disponible', // Por defecto está disponible
    estado: [],
    altura: '',
    medidas: {
      busto: '',
      cintura: '',
      cadera: ''
    },
    biografia: '',
    imagen_perfil: null,
    galeria: [],
    dias_disponibles: {
      lunes: false,
      martes: false,
      miercoles: false,
      jueves: false,
      viernes: false,
      sabado: false,
      domingo: false
    },
    ofertas_dias: {} // Nuevo campo para las ofertas y sus días aplicables
  });

  // Estado para manejar las previsualizaciones de imágenes
  const [imagePreviews, setImagePreviews] = useState({
    imagen_perfil: null,
    galeria: []
  });

  // Estado para manejar errores
  const [errors, setErrors] = useState({});
  // Estado para manejar el envío del formulario
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Cargar datos del modelo si estamos editando
  useEffect(() => {
    if (isEditing && modelo) {
      // Inicializar días disponibles desde el modelo o usar valores predeterminados
      let diasDisponibles = {
        lunes: false,
        martes: false,
        miercoles: false,
        jueves: false,
        viernes: false,
        sabado: false,
        domingo: false
      };

      // Si dias_disponibles existe, intentar parsearlo si es un string JSON
      if (modelo.dias_disponibles) {
        try {
          // Si es un string, intentar parsearlo como JSON
          if (typeof modelo.dias_disponibles === 'string') {
            diasDisponibles = JSON.parse(modelo.dias_disponibles);
          }
          // Si ya es un objeto, usarlo directamente
          else if (typeof modelo.dias_disponibles === 'object') {
            diasDisponibles = modelo.dias_disponibles;
          }
        } catch (error) {
          logger.error('Error al parsear dias_disponibles:', error);
        }
      }

      // Procesar el campo estado
      let estadoArray = [];
      if (modelo.estado) {
        if (typeof modelo.estado === 'string') {
          // Si es un string, dividirlo por comas
          estadoArray = modelo.estado.split(',').map(item => item.trim());
        } else if (Array.isArray(modelo.estado)) {
          // Si ya es un array, usarlo directamente
          estadoArray = modelo.estado;
        }
      }

      // Verificar si VIP o Destacada están en el array de estado
      const isVIP = estadoArray.includes('VIP');
      const isDestacada = estadoArray.includes('Destacada');

      // Preparar las previsualizaciones de imágenes existentes
      const newImagePreviews = { imagen_perfil: null, galeria: [] };

      if (modelo.imagen_perfil) {
        newImagePreviews.imagen_perfil = modelosService.getImageUrl(modelo, modelo.imagen_perfil);
      }

      if (modelo.galeria && modelo.galeria.length > 0) {
        newImagePreviews.galeria = modelo.galeria.map(img => ({
          url: modelosService.getImageUrl(modelo, img),
          filename: img
        }));
      }

      // Actualizar las previsualizaciones
      setImagePreviews(newImagePreviews);

      // Procesar las medidas
      let medidasObj = {
        busto: '',
        cintura: '',
        cadera: ''
      };

      if (modelo.medidas) {
        try {
          // Si es un string, intentar parsearlo como JSON
          if (typeof modelo.medidas === 'string') {
            medidasObj = JSON.parse(modelo.medidas);
          }
          // Si ya es un objeto, usarlo directamente
          else if (typeof modelo.medidas === 'object') {
            medidasObj = modelo.medidas;
          }
        } catch (error) {
          logger.error('Error al parsear medidas:', error);
          // Si hay error, intentar usar los campos individuales si existen
          if (modelo.busto) medidasObj.busto = modelo.busto;
          if (modelo.cintura) medidasObj.cintura = modelo.cintura;
          if (modelo.cadera) medidasObj.cadera = modelo.cadera;
        }
      } else {
        // Si no hay campo medidas, intentar usar los campos individuales
        if (modelo.busto) medidasObj.busto = modelo.busto;
        if (modelo.cintura) medidasObj.cintura = modelo.cintura;
        if (modelo.cadera) medidasObj.cadera = modelo.cadera;
      }

      // Determinar el estado de disponibilidad
      let disponibilidadValue = 'Disponible'; // Valor por defecto

      if (modelo.disponibilidad) {
        // Si existe el campo disponibilidad, usarlo
        disponibilidadValue = modelo.disponibilidad;
      } else if (modelo.disponible !== undefined) {
        // Si no existe disponibilidad pero sí disponible, convertir el booleano
        disponibilidadValue = modelo.disponible ? 'Disponible' : 'No disponible';
      }

      // Inicializar ofertas_dias desde el modelo o usar un objeto vacío
      let ofertasDias = {};
      if (modelo.ofertas_dias) {
        try {
          // Si es un string, intentar parsearlo como JSON
          if (typeof modelo.ofertas_dias === 'string') {
            ofertasDias = JSON.parse(modelo.ofertas_dias);
          }
          // Si ya es un objeto, usarlo directamente
          else if (typeof modelo.ofertas_dias === 'object') {
            ofertasDias = modelo.ofertas_dias;
          }
        } catch (error) {
          logger.error('Error al parsear ofertas_dias:', error);
        }
      }

      setFormData({
        nombre: modelo.nombre || '',
        edad: modelo.edad || '',
        perfil_corporal: modelo.perfil_corporal || '',
        destacada: isDestacada || modelo.destacada || false,
        vip: isVIP || modelo.vip || false,
        disponibilidad: disponibilidadValue,
        estado: estadoArray,
        altura: modelo.altura || '',
        medidas: medidasObj,
        biografia: modelo.biografia || '',
        imagen_perfil: null,
        galeria: [],
        dias_disponibles: diasDisponibles,
        ofertas_dias: ofertasDias
      });
    }
  }, [modelo, isEditing]);

  // Manejar cambios en los campos del formulario
  const handleChange = (e) => {
    const { name, value, type, files } = e.target;

    if (type === 'file') {
      if (name === 'galeria') {
        const fileArray = Array.from(files);
        // Add new files to formData.galeria
        setFormData(prev => ({
          ...prev,
          galeria: [...prev.galeria, ...fileArray] 
        }));

        // Create previews for new gallery images
        const newPreviews = fileArray.map(file => ({
          url: URL.createObjectURL(file),
          file: file // Keep the File object for submission
        }));
        setImagePreviews(prev => ({
          ...prev,
          galeria: [...prev.galeria, ...newPreviews]
        }));
      } else {
        // For single file (imagen_perfil)
        const file = files[0];
        if (file) {
          setFormData(prev => ({
            ...prev,
            [name]: file 
          }));
          setImagePreviews(prev => ({
            ...prev,
            [name]: URL.createObjectURL(file)
          }));
        } else { // If file is cleared
          setFormData(prev => ({ ...prev, [name]: null }));
          setImagePreviews(prev => ({ ...prev, [name]: null }));
        }
      }
    } else if (name === 'busto' || name === 'cintura' || name === 'cadera') {
      // Manejar cambios en las medidas
      setFormData(prev => ({
        ...prev,
        medidas: {
          ...prev.medidas,
          [name]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Función para eliminar una imagen
  const handleRemoveImage = (type, index = null) => {
    if (type === 'galeria' && index !== null) {
      const removedPreview = imagePreviews.galeria[index];
      
      // Update imagePreviews
      const updatedPreviews = imagePreviews.galeria.filter((_, i) => i !== index);
      setImagePreviews(prev => ({
        ...prev,
        galeria: updatedPreviews
      }));

      // Update formData.galeria
      // If the removed image was an existing one (had a filename), we mark it for deletion.
      // If it was a new File object, we just remove it from the list.
      setFormData(prev => {
        const updatedGaleria = prev.galeria.filter(item => {
          if (item instanceof File && removedPreview.file === item) {
            return false; // It's a new file, remove it
          }
          if (typeof item === 'string' && removedPreview.filename === item) {
            // This logic will be handled during submission by comparing with original
            // For now, we keep it to know it was an original image.
            // Or, we can remove it here and rely on comparison with original `modelo.galeria` at submit.
            // Let's remove it from formData.galeria for simplicity here.
            // The actual "deletion" from server happens by not including its filename in the update.
            return false; 
          }
          // If it's a string filename that doesn't match, or a File object that doesn't match, keep it.
          // This part needs careful handling during submission.
          // For now, let's simplify: if a preview is removed, the corresponding item in formData.galeria is removed.
          // This means formData.galeria will only contain NEW files to upload or existing filenames to KEEP.
          // This might be incorrect if `prev.galeria` items don't directly map to `imagePreviews.galeria` items
          // if `formData.galeria` was not reset properly on edit load.
          // Let's assume `formData.galeria` is initially empty on edit and only new files are added.
          // This is what the current `useEffect` and `handleChange` for gallery implies.
          return true; // This needs to be more robust.
        });

        // A more robust way for formData.galeria:
        // It should reflect the files to be submitted.
        // When an image is removed from preview, if it was a *new* File, remove it from formData.galeria.
        // If it was an *existing* image (identified by filename), its filename should be removed from the list of files to keep.
        // This is better handled at submission time.
        // For now, let's just filter imagePreviews and formData.galeria based on index.
        // This assumes `formData.galeria` only holds `File` objects for *new* images.
        // Existing images are identified by `imagePreviews.galeria[i].filename`.

        const newFormDataGaleria = prev.galeria.filter((fileOrFilename, i) => {
            // If the removed preview was a new file, remove the corresponding File object
            if (removedPreview.file && fileOrFilename instanceof File && fileOrFilename === removedPreview.file) {
                return false;
            }
            // If the removed preview was an existing file, we will handle its removal during submission
            // by not including its filename in the final list. So, we don't remove string filenames here.
            // However, the current `formData.galeria` only stores new files.
            // This part of the logic is tricky because `formData.galeria` is only populated with new files.
            // The actual list of files to send to the server will be constructed during `handleSubmit`.
            // So, for `handleRemoveImage`, we primarily care about `imagePreviews`.
            // `formData.galeria` will be rebuilt in `handleSubmit`.
            return true; // Placeholder, actual logic in handleSubmit
        });
        
        // Let's simplify: `formData.galeria` will only store NEW files.
        // When a preview is removed, if it's a new file, remove it from `formData.galeria`.
        const updatedFormDataGaleria = prev.galeria.filter(file => {
            if (removedPreview.file && file === removedPreview.file) {
                return false; // It's the new file we are removing
            }
            return true; // Keep other new files
        });

        return { ...prev, galeria: updatedFormDataGaleria };

      });

    } else if (type === 'imagen_perfil') {
      // Eliminar imagen de perfil
      setImagePreviews(prev => ({ ...prev, imagen_perfil: null }));
      setFormData(prev => ({ ...prev, imagen_perfil: null })); // Mark for removal or no new file
    }
  };

  // Manejar cambios en los switches Destacada, VIP y Disponibilidad
  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;

    // Actualizar el estado normal
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));

    // Actualizar el campo estado para VIP y Destacada
    if (name === 'vip' || name === 'destacada') {
      const estadoValue = name === 'vip' ? 'VIP' : 'Destacada';

      setFormData(prev => {
        let newEstado = [...prev.estado];

        if (checked && !newEstado.includes(estadoValue)) {
          // Agregar al array si está marcado y no existe
          newEstado.push(estadoValue);
        } else if (!checked && newEstado.includes(estadoValue)) {
          // Quitar del array si no está marcado y existe
          newEstado = newEstado.filter(item => item !== estadoValue);
        }

        return {
          ...prev,
          estado: newEstado
        };
      });
    }
  };

  // Manejar cambios en la disponibilidad
  const toggleDisponibilidad = () => {
    setFormData(prev => ({
      ...prev,
      disponibilidad: prev.disponibilidad === 'Disponible' ? 'No disponible' : 'Disponible'
    }));
  };

  // Manejar clics en los badges de días
  const handleDayBadgeClick = (day) => {
    setFormData(prev => ({
      ...prev,
      dias_disponibles: {
        ...prev.dias_disponibles,
        [day]: !prev.dias_disponibles[day]
      }
    }));
  };



  // Validar el formulario
  const validateForm = () => {
    const newErrors = {};

    if (!formData.nombre) newErrors.nombre = 'El nombre es obligatorio';
    if (!formData.edad) newErrors.edad = 'La edad es obligatoria';
    if (!formData.perfil_corporal) newErrors.perfil_corporal = 'El perfil corporal es obligatorio';

    // Si no estamos editando, la imagen de perfil es obligatoria
    if (!isEditing && !formData.imagen_perfil) {
      newErrors.imagen_perfil = 'La imagen de perfil es obligatoria';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manejar envío del formulario
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const dataForService = {
        nombre: formData.nombre,
        edad: formData.edad,
        perfil_corporal: formData.perfil_corporal,
        altura: formData.altura,
        biografia: formData.biografia,
        disponibilidad: formData.disponibilidad, // 'Disponible' o 'No disponible'
        dias_disponibles: formData.dias_disponibles, // Objeto
        medidas: formData.medidas, // Objeto
        ofertas_dias: formData.ofertas_dias, // Objeto
      };

      // Preparar el campo estado (array de strings)
      const estadoArray = [];
      if (formData.vip) estadoArray.push('VIP');
      if (formData.destacada) estadoArray.push('Destacada');
      dataForService.estado = estadoArray;


      // Manejar imagen_perfil
      if (formData.imagen_perfil instanceof File) {
        // Nueva imagen de perfil seleccionada
        dataForService.imagen_perfil = formData.imagen_perfil;
      } else if (isEditing && modelo && modelo.imagen_perfil && imagePreviews.imagen_perfil === null) {
        // Imagen de perfil existente fue eliminada (preview es null, y había una original)
        dataForService.imagen_perfil = null; // Señal para eliminar
      }
      // Si no hay formData.imagen_perfil (File) y imagePreviews.imagen_perfil no es null (o no estamos editando),
      // significa que se mantiene la imagen existente o no se sube ninguna (en creación),
      // por lo que no se añade `imagen_perfil` a `dataForService` para no sobreescribir.

      // Manejar galeria
      const finalGalleryElements = [];
      let galleryChanged = false;
      const originalGalleryFilenames = isEditing && modelo && modelo.galeria ? modelo.galeria : [];

      if (imagePreviews.galeria.length > 0) {
        imagePreviews.galeria.forEach(preview => {
          if (preview.file instanceof File) { // Es una nueva imagen
            finalGalleryElements.push(preview.file);
            galleryChanged = true;
          } else if (preview.filename) { // Es una imagen existente que se quiere conservar
            finalGalleryElements.push(preview.filename);
          }
        });
      }
      
      // Detectar si la galería cambió respecto a la original
      if (isEditing) {
        if (finalGalleryElements.length !== originalGalleryFilenames.length) {
          galleryChanged = true;
        } else {
          // Comprobar si los nombres de archivo y el orden son los mismos
          const currentFilenames = finalGalleryElements.filter(el => typeof el === 'string');
          if (currentFilenames.length !== originalGalleryFilenames.length || 
              !currentFilenames.every((filename, idx) => filename === originalGalleryFilenames[idx])) {
             // This comparison is tricky if order can change or new files are mixed.
             // A simpler check: if any new files were added, or if count of existing files differs.
             if (finalGalleryElements.some(el => el instanceof File) || currentFilenames.length !== originalGalleryFilenames.length) {
                 galleryChanged = true;
             } else {
                 // Check if the set of kept filenames is different from original
                 const keptFilenamesSet = new Set(currentFilenames);
                 const originalFilenamesSet = new Set(originalGalleryFilenames);
                 if (keptFilenamesSet.size !== originalFilenamesSet.size || !Array.from(keptFilenamesSet).every(fn => originalFilenamesSet.has(fn))) {
                     galleryChanged = true;
                 }
             }
          }
        }
      } else if (finalGalleryElements.length > 0) { // Creando y hay imágenes
        galleryChanged = true;
      }


      if (galleryChanged) {
        if (finalGalleryElements.length > 0) {
          dataForService.galeria = finalGalleryElements;
        } else {
          // Todas las imágenes fueron eliminadas
          dataForService.galeria = null; // Señal para vaciar la galería
        }
      }
      // Si galleryChanged es false, no se añade `galeria` a `dataForService` para no sobreescribirla.

      logger.debug('Datos a enviar al servicio:', dataForService);

      let result;
      if (isEditing) {
        result = await modelosService.update(modelo.id, dataForService);
      } else {
        result = await modelosService.create(dataForService);
      }

      if (onSubmit) {
        onSubmit(result);
      }

      // Redirigir a la página de administración
      navigate('/admin');
    } catch (error) {
      logger.error('Error al guardar modelo:', error);

      // Mostrar detalles del error si están disponibles
      if (error.data && error.data.data) {
        logger.error('Detalles del error:', error.data.data);

        // Mostrar el error completo para depuración
        logger.error('Error completo:', JSON.stringify(error, null, 2));

        // Mostrar errores específicos de campos
        const fieldErrors = error.data.data;
        Object.keys(fieldErrors).forEach(field => {
          logger.error(`Error en campo ${field}:`, fieldErrors[field]);
          setErrors(prev => ({
            ...prev,
            [field]: fieldErrors[field].message || JSON.stringify(fieldErrors[field])
          }));
        });
      } else {
        logger.error('Error sin detalles:', error);
        setErrors({ submit: 'Error al guardar la brujita. Inténtalo de nuevo.' });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="bg-[#181828] p-8 rounded-2xl shadow-xl border border-[#29293d] max-w-2xl mx-auto">
        <BasicInfoSection
          formData={formData}
          errors={errors}
          handleChange={handleChange}
          handleSwitchChange={handleSwitchChange}
          toggleDisponibilidad={toggleDisponibilidad}
        />

        <MeasurementsSection
          formData={formData}
          handleChange={handleChange}
        />

        <PersonalInfoSection
          formData={formData}
          handleChange={handleChange}
        />

        <AvailabilitySection
          formData={formData}
          handleDayBadgeClick={handleDayBadgeClick}
        />

        <OfertasDiasSection
          formData={formData}
          setFormData={setFormData}
        />

        <ImageUploadSection
          imagePreviews={imagePreviews}
          errors={errors}
          handleChange={handleChange}
          handleRemoveImage={handleRemoveImage}
          isEditing={isEditing}
        />

        <FormActions
          isSubmitting={isSubmitting}
          isEditing={isEditing}
          errors={errors}
        />
      </div>
    </form>
  );
};

export default ModeloForm;
