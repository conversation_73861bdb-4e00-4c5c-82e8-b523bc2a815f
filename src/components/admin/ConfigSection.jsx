import React, { useState, useEffect } from 'react';
import { configService } from '../../services/configService';
import logger from '../../services/logService';

const ConfigSection = ({ config, loading, error, onSave }) => {
  const [formData, setFormData] = useState({
    telegram: '',
    wa: '',
    tiktok: '',
    twitter: ''
  });
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState({ type: '', text: '' });
  const [configId, setConfigId] = useState('');

  // Cargar datos iniciales
  useEffect(() => {
    if (config && config.items && config.items.length > 0) {
      const configItem = config.items[0];
      setFormData({
        telegram: configItem.telegram || '',
        wa: configItem.wa || '',
        tiktok: configItem.tiktok || '',
        twitter: configItem.twitter || ''
      });
      setConfigId(configItem.id);
    }
  }, [config]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    setSaveMessage({ type: '', text: '' });

    try {
      // Obtener los valores actuales de la configuración
      let currentConfig = {};
      if (config && config.items && config.items.length > 0) {
        currentConfig = config.items[0];
      }

      // Crear un objeto con solo los campos que han sido modificados
      // y no enviar campos vacíos que borrarían los valores existentes
      const dataToUpdate = {};
      Object.entries(formData).forEach(([key, value]) => {
        // Solo incluir el campo si tiene un valor o si es diferente al valor actual
        if (value && value.trim() !== '') {
          dataToUpdate[key] = value;
        }
      });

      // Si no hay configId, intentar crear una configuración inicial
      if (!configId) {
        try {
          const newConfig = await configService.createInitialConfig();
          setConfigId(newConfig.id);

          // Actualizar con los datos del formulario
          await configService.updateConfig(newConfig.id, dataToUpdate);
        } catch (createError) {
          logger.error('Error al crear configuración inicial:', createError);
          throw createError; // Propagar el error para que se maneje en el catch principal
        }
      } else {
        // Si hay configId, actualizar la configuración existente
        await configService.updateConfig(configId, dataToUpdate);
      }

      setSaveMessage({
        type: 'success',
        text: 'Configuración guardada correctamente'
      });

      // Notificar al componente padre para actualizar los datos
      if (onSave) {
        onSave();
      }
    } catch (error) {
      logger.error('Error al guardar configuración:', error);
      setSaveMessage({
        type: 'error',
        text: 'Error al guardar la configuración. Por favor, intenta de nuevo.'
      });
    } finally {
      setIsSaving(false);

      // Limpiar el mensaje después de 5 segundos
      setTimeout(() => {
        setSaveMessage({ type: '', text: '' });
      }, 5000);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="w-16 h-16 relative animate-pulse-glow">
          <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#4caf50] animate-spin"></div>
          <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#2e7d32] animate-spin animate-reverse"></div>
        </div>
        <p className="mt-4 text-gray-400">Cargando configuración...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-[#181828] p-8 rounded-lg shadow-lg text-center border border-[#29293d]">
        <p className="text-red-400 mb-4">Error al cargar la configuración: {error}</p>
        <button
          onClick={onSave}
          className="px-4 py-2 bg-[#e91e63] text-white rounded-md hover:bg-[#d81557] transition-colors"
        >
          Reintentar
        </button>
      </div>
    );
  }

  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-[#4caf50] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#2e7d32]">⚙️</span> Configuración de Enlaces
        </h2>
      </div>

      <div className="bg-[#181828] p-6 rounded-lg shadow-lg border border-[#29293d]">
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-white mb-4">Enlaces para Botones de Contacto</h3>
            <p className="text-gray-400 mb-2">
              Configura los enlaces que se utilizarán en los botones de contacto y en el pie de página.
            </p>
           
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-300 mb-2" htmlFor="wa">
                  WhatsApp (URL completa)
                </label>
                <input
                  type="text"
                  id="wa"
                  name="wa"
                  value={formData.wa}
                  onChange={handleInputChange}
                  placeholder={config?.items?.[0]?.wa || "https://wa.me/+52XXXXXXXXXX"}
                  className="w-full px-4 py-2 bg-[#1e1e2e] border border-[#3d3d5a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#4caf50] focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Ejemplo: https://wa.me/+527205373745
                </p>
              </div>

              <div>
                <label className="block text-gray-300 mb-2" htmlFor="telegram">
                  Telegram (URL completa)
                </label>
                <input
                  type="text"
                  id="telegram"
                  name="telegram"
                  value={formData.telegram}
                  onChange={handleInputChange}
                  placeholder={config?.items?.[0]?.telegram || "https://t.me/usuario"}
                  className="w-full px-4 py-2 bg-[#1e1e2e] border border-[#3d3d5a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#4caf50] focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Ejemplo: https://t.me/BrujitasMx
                </p>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-semibold text-white mb-4">Enlaces para Redes Sociales</h3>
          

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-300 mb-2" htmlFor="tiktok">
                  TikTok (URL completa)
                </label>
                <input
                  type="text"
                  id="tiktok"
                  name="tiktok"
                  value={formData.tiktok}
                  onChange={handleInputChange}
                  placeholder={config?.items?.[0]?.tiktok || "https://tiktok.com/@usuario"}
                  className="w-full px-4 py-2 bg-[#1e1e2e] border border-[#3d3d5a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#4caf50] focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2" htmlFor="twitter">
                  Twitter/X (URL completa)
                </label>
                <input
                  type="text"
                  id="twitter"
                  name="twitter"
                  value={formData.twitter}
                  onChange={handleInputChange}
                  placeholder={config?.items?.[0]?.twitter || "https://twitter.com/usuario"}
                  className="w-full px-4 py-2 bg-[#1e1e2e] border border-[#3d3d5a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#4caf50] focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {saveMessage.text && (
            <div className={`mb-4 p-3 rounded-md ${
              saveMessage.type === 'success'
                ? 'bg-green-900 bg-opacity-20 text-green-400 border border-green-900'
                : 'bg-red-900 bg-opacity-20 text-red-400 border border-red-900'
            }`}>
              {saveMessage.text}
            </div>
          )}

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSaving}
              className="px-6 py-3 bg-gradient-to-r from-[#2e7d32] to-[#4caf50] text-white rounded-lg hover:shadow-lg hover:shadow-[#4caf50]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 flex items-center gap-2 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSaving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Guardando...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Guardar Configuración
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default ConfigSection;
