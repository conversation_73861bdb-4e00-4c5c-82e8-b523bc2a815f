import React, { useState, useEffect } from 'react';
import { modelosService } from '../../services/pocketbase';
import logger from '../../services/logService';
import { API_URL } from '../../config';
import LazyImage from '../LazyImage';

const OfertaModelosDiasManager = ({ oferta, onClose, onSave }) => {
  const [modelos, setModelos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [modelosOfertasDias, setModelosOfertasDias] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredModelos, setFilteredModelos] = useState([]);

  const days = ['lunes', 'martes', 'miercoles', 'jueves', 'viernes', 'sabado', 'domingo'];
  const dayLabels = ['Lun', 'Mar', 'Mi<PERSON>', '<PERSON><PERSON>', 'Vie', 'Sáb', 'Dom'];

  // Cargar modelos y sus datos de ofertas_dias
  useEffect(() => {
    const fetchModelos = async () => {
      try {
        setLoading(true);
        const response = await modelosService.getAll();
        const modelosList = response.items || [];
        setModelos(modelosList);
        setFilteredModelos(modelosList);

        // Inicializar el estado de ofertas_dias para cada modelo
        const initialOfertasDias = {};
        
        for (const modelo of modelosList) {
          let ofertasDias = {};
          
          // Intentar parsear el campo ofertas_dias si existe
          if (modelo.ofertas_dias) {
            try {
              if (typeof modelo.ofertas_dias === 'string') {
                ofertasDias = JSON.parse(modelo.ofertas_dias);
              } else if (typeof modelo.ofertas_dias === 'object') {
                ofertasDias = modelo.ofertas_dias;
              }
            } catch (err) {
              logger.error(`Error al parsear ofertas_dias para modelo ${modelo.id}:`, err);
            }
          }
          
          initialOfertasDias[modelo.id] = ofertasDias;
        }
        
        setModelosOfertasDias(initialOfertasDias);
        setError(null);
      } catch (err) {
        logger.error('Error al cargar modelos:', err);
        setError('No se pudieron cargar los modelos. Por favor, intenta de nuevo.');
      } finally {
        setLoading(false);
      }
    };

    fetchModelos();
  }, []);

  // Filtrar modelos cuando cambia el término de búsqueda
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredModelos(modelos);
    } else {
      const filtered = modelos.filter(modelo => 
        modelo.nombre.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredModelos(filtered);
    }
  }, [searchTerm, modelos]);

  // Manejar selección de días para un modelo
  const handleDayClick = (modeloId, day) => {
    setModelosOfertasDias(prev => {
      const modeloOfertasDias = { ...prev[modeloId] };
      
      // Inicializar el array para esta oferta si no existe
      if (!modeloOfertasDias[oferta.id]) {
        modeloOfertasDias[oferta.id] = [];
      }
      
      // Verificar si el día ya está seleccionado
      const isSelected = modeloOfertasDias[oferta.id].includes(day);
      
      if (isSelected) {
        // Si ya está seleccionado, quitarlo
        modeloOfertasDias[oferta.id] = modeloOfertasDias[oferta.id].filter(d => d !== day);
      } else {
        // Si no está seleccionado, agregarlo
        modeloOfertasDias[oferta.id] = [...modeloOfertasDias[oferta.id], day];
      }
      
      // Si no quedan días seleccionados, eliminar la entrada para esta oferta
      if (modeloOfertasDias[oferta.id].length === 0) {
        delete modeloOfertasDias[oferta.id];
      }
      
      return {
        ...prev,
        [modeloId]: modeloOfertasDias
      };
    });
  };

  // Guardar los cambios
  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      
      // Guardar los cambios para cada modelo
      const promises = Object.entries(modelosOfertasDias).map(async ([modeloId, ofertasDias]) => {
        // Crear FormData para enviar al servidor
        const formData = new FormData();
        
        // Agregar ofertas_dias como JSON
        formData.append('ofertas_dias', JSON.stringify(ofertasDias));
        
        // Actualizar el modelo
        return modelosService.update(modeloId, formData);
      });
      
      await Promise.all(promises);
      
      if (onSave) {
        onSave();
      }
    } catch (err) {
      logger.error('Error al guardar los días de oferta:', err);
      setError('No se pudieron guardar los cambios. Por favor, intenta de nuevo.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-[#181828] rounded-xl shadow-2xl border border-[#29293d] w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Encabezado */}
        <div className="p-4 border-b border-[#29293d] flex justify-between items-center">
          <h2 className="text-xl font-bold text-[#e0b3ff]">
            Gestionar días para oferta: <span className="text-white">{oferta.titulo}</span>
          </h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Barra de búsqueda */}
        <div className="p-4 border-b border-[#29293d]">
          <input
            type="text"
            placeholder="Buscar brujita por nombre..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full p-3 rounded-lg bg-[#23233a] border border-[#29293d] text-white placeholder-gray-400 focus:ring-2 focus:ring-[#9c27b0] focus:border-transparent"
          />
        </div>
        
        {/* Contenido */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-400">{error}</p>
              <button 
                onClick={() => setError(null)}
                className="mt-4 px-4 py-2 bg-[#23233a] text-white rounded-lg hover:bg-[#29293d]"
              >
                Intentar de nuevo
              </button>
            </div>
          ) : filteredModelos.length === 0 ? (
            <p className="text-center text-gray-400 py-8">No se encontraron brujitas</p>
          ) : (
            <div className="space-y-6">
              {filteredModelos.map(modelo => (
                <div key={modelo.id} className="bg-[#23233a] p-4 rounded-xl border border-[#29293d]">
                  <div className="flex items-center mb-3">
                    {/* Imagen de perfil */}
                    <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-[#9c27b0] mr-3">
                      {modelo.imagen_perfil ? (
                        <LazyImage
                          src={`${API_URL}/api/files/brujitas/${modelo.id}/${modelo.imagen_perfil}`}
                          alt={modelo.nombre}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-[#2d1949] flex items-center justify-center text-white text-xs">
                          Sin foto
                        </div>
                      )}
                    </div>
                    
                    {/* Nombre */}
                    <h3 className="text-lg font-medium text-white">{modelo.nombre}</h3>
                  </div>
                  
                  {/* Selección de días */}
                  <div className="grid grid-cols-7 gap-2">
                    {days.map((day, index) => {
                      const isSelected = modelosOfertasDias[modelo.id]?.[oferta.id]?.includes(day);
                      
                      return (
                        <button
                          key={`${modelo.id}-${day}`}
                          type="button"
                          onClick={() => handleDayClick(modelo.id, day)}
                          className={`w-full py-2 rounded-md text-sm font-medium transition-all ${
                            isSelected 
                              ? 'bg-gradient-to-r from-[#e91e63]/20 to-[#9c27b0]/20 text-white border border-[#e91e63]' 
                              : 'bg-[#29293d] text-gray-400 hover:bg-[#333344]'
                          }`}
                        >
                          {dayLabels[index]}
                        </button>
                      );
                    })}
                  </div>
                  
                  <div className="mt-2 text-xs text-gray-400">
                    {modelosOfertasDias[modelo.id]?.[oferta.id]?.length
                      ? `Días seleccionados: ${modelosOfertasDias[modelo.id][oferta.id].length}`
                      : "No hay días seleccionados para esta oferta"}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Pie */}
        <div className="p-4 border-t border-[#29293d] flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-[#29293d] rounded-lg text-gray-300 hover:bg-[#23233a] transition-colors"
            disabled={saving}
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-lg hover:opacity-90 transition-colors flex items-center"
            disabled={saving}
          >
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Guardando...
              </>
            ) : (
              'Guardar Cambios'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default OfertaModelosDiasManager;
