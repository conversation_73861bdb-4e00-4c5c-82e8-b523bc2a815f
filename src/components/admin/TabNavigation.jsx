// src/components/admin/TabNavigation.jsx
import React from 'react';

const TabButton = ({ tabKey, activeTab, onClick, label, icon, color, gradientFrom, gradientTo, loading }) => {
  const isActive = activeTab === tabKey;
  const activeTextColor = `text-[${color}]`;
  const borderColorClass = `border-[${color}]`;
  // Use specific gradient colors if provided, otherwise fallback to a single color
  const gradientClass = gradientFrom && gradientTo
    ? `bg-gradient-to-r from-[${gradientFrom}] to-[${gradientTo}]`
    : `bg-[${color}]`; // Fallback to a solid color using the main 'color' prop

  return (
    <button
      onClick={() => onClick(tabKey)}
      className={`px-6 py-3 text-sm font-medium transition-all flex items-center gap-2 relative ${
        isActive ? `${activeTextColor} font-semibold` : 'text-gray-400 hover:text-gray-300'
      }`}
      disabled={loading && isActive}
    >
      {icon && <span className="mr-2">{icon}</span>}
      {label}
      {loading && isActive && (
        <span className={`ml-2 inline-block w-3 h-3 border-2 ${borderColorClass} border-t-transparent rounded-full animate-spin`}></span>
      )}
      {isActive && (
        <span className={`absolute bottom-0 left-0 w-full h-0.5 ${gradientClass}`}></span>
      )}
    </button>
  );
};

const TabNavigation = ({ activeTab, onTabChange, loadingStates }) => {
  const tabs = [
    {
      key: 'brujitas',
      label: 'Brujitas',
      icon: '✨',
      color: '#e0b3ff', // Main text color for active tab
      gradientFrom: '#9c27b0',
      gradientTo: '#e91e63',
      loading: loadingStates.brujitas
    },
    {
      key: 'independientes',
      label: 'Independientes',
      icon: '💎',
      color: '#64b5f6',
      gradientFrom: '#1e88e5',
      gradientTo: '#0d47a1',
      loading: loadingStates.independientes
    },
    {
      key: 'banners',
      label: 'Banners',
      icon: '🖼️',
      color: '#ff9800',
      gradientFrom: '#ff9800',
      gradientTo: '#f44336',
      loading: loadingStates.banners
    },
    {
      key: 'ofertas',
      label: 'Ofertas',
      icon: '🔥',
      color: '#e91e63',
      gradientFrom: '#e91e63',
      gradientTo: '#9c27b0',
      loading: loadingStates.ofertas
    },
    {
      key: 'config',
      label: 'Enlaces',
      icon: '⚙️',
      color: '#4caf50',
      gradientFrom: '#2e7d32',
      gradientTo: '#4caf50',
      loading: loadingStates.config
    },
  ];

  return (
    <div className="mb-6">
      <div className="flex flex-wrap border-b border-[#29293d] mb-2">
        {tabs.map((tab) => (
          <TabButton
            key={tab.key}
            tabKey={tab.key}
            activeTab={activeTab}
            onClick={onTabChange}
            label={tab.label}
            icon={tab.icon}
            color={tab.color}
            gradientFrom={tab.gradientFrom}
            gradientTo={tab.gradientTo}
            loading={tab.loading}
          />
        ))}
      </div>
    </div>
  );
};

export default TabNavigation;
