// src/components/admin/OfertasSection.jsx
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import OfertaModelosDiasManager from './OfertaModelosDiasManager';
import DeleteConfirmationModal from '../DeleteConfirmationModal';

const OfertasSection = ({
  ofertas,
  loading,
  error,
  onToggleVisibility,
  onDeleteOferta,
  setOfertaError
}) => {
  const [selectedOferta, setSelectedOferta] = useState(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [ofertaToDelete, setOfertaToDelete] = useState(null);
  const [isDeletingOferta, setIsDeletingOferta] = useState(false);

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="w-16 h-16 relative animate-pulse-glow">
          <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#e91e63] animate-spin"></div>
          <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#9c27b0] animate-spin animate-reverse"></div>
        </div>
        <p className="mt-4 text-gray-400">Cargando ofertas...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-[#2d0a16] p-6 rounded-lg border border-red-800 mb-6">
        <h3 className="text-xl font-semibold text-red-400 mb-2">Error</h3>
        <p className="text-red-300">{error}</p>
      </div>
    );
  }

  // Common header for both empty and populated states
  const headerContent = (
    <div className="flex justify-between items-center mb-6">
      <h2 className="text-xl font-bold text-[#e0b3ff] drop-shadow-sm flex items-center gap-2">
        <span className="text-[#9c27b0]">🔥</span> Gestión de Ofertas
      </h2>
      <Link
        to="/admin/nueva-oferta"
        className="px-6 py-3 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-lg hover:shadow-lg hover:shadow-[#9c27b0]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 flex items-center gap-2 font-medium"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
        </svg>
        <span>Nueva Oferta</span>
      </Link>
    </div>
  );

  if (ofertas.length === 0) {
    return (
      <>
        {headerContent}
        <div className="bg-[#181828] p-8 rounded-lg shadow-lg text-center border border-[#29293d]">
          <p className="text-gray-300 mb-4">No hay ofertas registradas todavía.</p>
        </div>
      </>
    );
  }

  return (
    <>
      {headerContent}
      <div className="bg-[#181828] rounded-lg shadow-lg border border-[#29293d] overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-[#23233a] border-b border-[#29293d]">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Título</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Descripción</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">Estado</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-[#29293d]">
              {ofertas.map((oferta) => (
                <tr key={oferta.id} className="bg-[#1e1e2e] hover:bg-[#23233a] transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-white">{oferta.titulo}</div>
                  </td>
                  <td className="px-6 py-4 max-w-sm">
                    <p className="text-sm text-gray-300 line-clamp-2">
                      {(() => {
                        // Verificar si es un objeto
                        if (typeof oferta.descripcion === 'object' && oferta.descripcion !== null) {
                          try {
                            // Intentar parsear como JSON si es un objeto
                            const secciones = Array.isArray(oferta.descripcion)
                              ? oferta.descripcion
                              : JSON.parse(JSON.stringify(oferta.descripcion));

                            if (Array.isArray(secciones) && secciones.length > 0) {
                              return `${secciones.length} ${secciones.length === 1 ? 'sección' : 'secciones'}`;
                            }
                            return 'Contenido estructurado';
                          } catch (error) {
                            return 'Contenido no disponible';
                          }
                        }

                        // Verificar si es un string que podría ser JSON
                        if (typeof oferta.descripcion === 'string') {
                          try {
                            if (oferta.descripcion.startsWith('[') || oferta.descripcion.startsWith('{')) {
                              const secciones = JSON.parse(oferta.descripcion);
                              if (Array.isArray(secciones) && secciones.length > 0) {
                                return `${secciones.length} ${secciones.length === 1 ? 'sección' : 'secciones'}`;
                              }
                            }
                            // Si no es JSON o no tiene el formato esperado, mostrar los primeros caracteres
                            return oferta.descripcion.length > 50
                              ? oferta.descripcion.substring(0, 50) + '...'
                              : oferta.descripcion;
                          } catch (error) {
                            // Si falla el parseo, mostrar como texto normal
                            return oferta.descripcion.length > 50
                              ? oferta.descripcion.substring(0, 50) + '...'
                              : oferta.descripcion;
                          }
                        }

                        // Para cualquier otro tipo de dato
                        return String(oferta.descripcion || 'Sin contenido');
                      })()}
                    </p>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <button
                      type="button"
                      onClick={async () => {
                        try {
                          await onToggleVisibility(oferta.id, !oferta.visible);
                        } catch (err) {
                          if (setOfertaError) {
                            setOfertaError('No se pudo cambiar la visibilidad. Intente de nuevo.');
                          }
                        }
                      }}
                      className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                        oferta.visible ? 'bg-green-500' : 'bg-gray-600'
                      }`}
                      title={oferta.visible ? 'Desactivar oferta' : 'Activar oferta'}
                    >
                      <span className="sr-only">Toggle oferta</span>
                      <span
                        className={`inline-block w-4 h-4 transform bg-white rounded-full transition-transform duration-200 ease-in-out ${
                          oferta.visible ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => setSelectedOferta(oferta)}
                        className="text-purple-400 hover:text-purple-300 transition-colors p-1"
                        title="Gestionar Días"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </button>
                      <Link
                        to={`/admin/editar-oferta/${oferta.id}`}
                        className="text-blue-400 hover:text-blue-300 transition-colors p-1"
                        title="Editar Oferta"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </Link>
                      <button
                        onClick={() => {
                          setOfertaToDelete(oferta);
                          setDeleteModalOpen(true);
                        }}
                        className="text-red-400 hover:text-red-300 transition-colors p-1"
                        title="Eliminar Oferta"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal para gestionar días de oferta */}
      {selectedOferta && (
        <OfertaModelosDiasManager
          oferta={selectedOferta}
          onClose={() => setSelectedOferta(null)}
          onSave={() => {
            setSelectedOferta(null);
            // Opcional: Mostrar mensaje de éxito
            if (setOfertaError) {
              setOfertaError(null); // Limpiar errores previos
            }
          }}
        />
      )}

      {/* Modal de confirmación para eliminar oferta */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setOfertaToDelete(null);
          setIsDeletingOferta(false);
        }}
        onConfirm={async () => {
          if (!ofertaToDelete) return;
          setIsDeletingOferta(true);
          try {
            await onDeleteOferta(ofertaToDelete.id);
            setDeleteModalOpen(false);
            setOfertaToDelete(null);
          } catch (err) {
            if (setOfertaError) {
              setOfertaError('No se pudo eliminar la oferta. Intente de nuevo.');
            }
          } finally {
            setIsDeletingOferta(false);
          }
        }}
        title="Confirmar eliminación"
        message={ofertaToDelete ? `¿Estás seguro de que deseas eliminar la oferta "${ofertaToDelete.titulo}"? Esta acción no se puede deshacer.` : ''}
        isDeleting={isDeletingOferta}
      />
    </>
  );
};

export default OfertasSection;
