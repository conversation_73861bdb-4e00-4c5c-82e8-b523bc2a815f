// src/components/admin/IndependientesSection.jsx
import React from 'react';
import { Link } from 'react-router-dom';
import { isEntityVIP, isEntityDestacada, getIndependienteZonasTrabajo } from '../../utils/adminUtils';
import { independientesService } from '../../services/independientesService'; // For getImageUrl

const IndependienteCard = ({ independiente, getImageUrl, onDisponibilidadChange, onDelete }) => {
  const zonasTrabajo = getIndependienteZonasTrabajo(independiente);
  const handleImageError = (e) => {
    e.target.src = '/img/placeholder.png';
  };

  return (
    <div key={independiente.id} className="bg-[#23233a] rounded-xl shadow-md overflow-hidden border border-[#29293d] hover:shadow-lg hover:border-[#3d3d3d] transition-all duration-300">
      <div className="relative h-40 bg-gradient-to-r from-[#1e88e5] to-[#0d47a1] overflow-hidden">
        <img
          src={getImageUrl(independiente, 'independiente')}
          alt={independiente.nombre}
          className="w-full h-full object-cover opacity-80 hover:opacity-100 transition-opacity duration-300"
          onError={handleImageError}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-[#121212] via-transparent to-transparent"></div>
        <div className="absolute top-2 right-2 flex flex-col gap-2">
          {isEntityVIP(independiente) && (
            <span className="px-2 py-1 text-xs rounded-md font-semibold bg-gradient-to-r from-[#e91e63] to-[#9c27b0] text-white shadow-lg flex items-center gap-1">
              <span>⭐</span> VIP
            </span>
          )}
          {isEntityDestacada(independiente) && (
            <span className="px-2 py-1 text-xs rounded-md font-semibold bg-gradient-to-r from-[#ff9800] to-[#f44336] text-white shadow-lg flex items-center gap-1">
              <span>🔥</span> Destacada
            </span>
          )}
        </div>
      </div>
      <div className="p-4 pt-8">
        <div className="mb-3">
          <div className="flex justify-between items-start">
            <h3 className="text-lg font-bold text-white">{independiente.nombre}</h3>
            <div className="flex items-center text-xs text-gray-400 bg-[#1a1a2e] px-2 py-1 rounded-md">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-[#64b5f6]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              {independiente.visitas || 0}
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <span>{independiente.edad} años</span>
            {independiente.perfil_corporal && (
              <>
                <span className="text-gray-500">•</span>
                <span>{independiente.perfil_corporal}</span>
              </>
            )}
            {independiente.altura && (
              <>
                <span className="text-gray-500">•</span>
                <span>{independiente.altura} cm</span>
              </>
            )}
          </div>
          {zonasTrabajo.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {zonasTrabajo.map((zona, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-[#29293d] text-gray-300"
                >
                  <span className="mr-1">📍</span>
                  {zona}
                </span>
              ))}
            </div>
          )}
        </div>
        <button
          onClick={() => onDisponibilidadChange(independiente, 'independiente')}
          className={`w-full mb-3 flex items-center justify-center gap-2 py-2 rounded-md text-sm font-medium transition-all ${(independiente.disponibilidad || 'Disponible') === 'Disponible' ? 'bg-gradient-to-r from-[#4CAF50]/20 to-[#2E7D32]/20 text-white' : 'bg-gradient-to-r from-[#f44336]/20 to-[#d32f2f]/20 text-white'}`}
        >
          <span>{(independiente.disponibilidad || 'Disponible') === 'Disponible' ? '🟢' : '🔴'}</span>
          <span>{independiente.disponibilidad || 'Disponible'}</span>
        </button>
        <div className="flex gap-2 justify-between">
          <Link to={`/independiente/${independiente.id}`} className="flex-1 py-2 bg-[#1a1a2e] text-[#e0b3ff] rounded-md text-sm hover:bg-[#252538] transition-colors border border-[#3d3d3d] flex items-center justify-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            Ver
          </Link>
          <Link to={`/admin/editar-independiente/${independiente.id}`} className="flex-1 py-2 bg-[#1a1a2e] text-[#64b5f6] rounded-md text-sm hover:bg-[#252538] transition-colors border border-[#3d3d3d] flex items-center justify-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Editar
          </Link>
          <button onClick={() => onDelete(independiente, 'independiente')} className="flex-1 py-2 bg-[#1a1a2e] text-[#ef5350] rounded-md text-sm hover:bg-[#252538] transition-colors border border-[#3d3d3d] flex items-center justify-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Eliminar
          </button>
        </div>
      </div>
    </div>
  );
};

const IndependientesSection = ({ independientes, loading, error, onDisponibilidadChange, onDelete, getImageUrl }) => {
  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="w-16 h-16 relative animate-pulse-glow">
          <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#1e88e5] animate-spin"></div>
          <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#0d47a1] animate-spin animate-reverse"></div>
        </div>
        <p className="mt-4 text-gray-400">Cargando independientes...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-[#2d0a16] p-6 rounded-lg border border-red-800 mb-6">
        <h3 className="text-xl font-semibold text-red-400 mb-2">Error</h3>
        <p className="text-red-300">{error}</p>
      </div>
    );
  }

  if (independientes.length === 0) {
    return (
      <div className="bg-[#181828] p-8 rounded-lg shadow-lg text-center border border-[#29293d]">
        <p className="text-gray-300 mb-4">No hay independientes registradas todavía.</p>
      </div>
    );
  }

  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-[#64b5f6] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#1e88e5]">💎</span> Independientes
        </h2>
        <Link
          to="/admin/nueva-independiente"
          className="px-6 py-3 bg-gradient-to-r from-[#1e88e5] to-[#0d47a1] text-white rounded-lg hover:shadow-lg hover:shadow-[#1e88e5]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 flex items-center gap-2 font-medium"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          <span>Nueva Independiente</span>
          <span className="ml-1 text-blue-200">💎</span>
        </Link>
      </div>
      <div className="p-4 bg-[#181828] rounded-lg shadow-lg border border-[#29293d]">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {independientes.map((independiente) => (
            <IndependienteCard
              key={independiente.id}
              independiente={independiente}
              getImageUrl={getImageUrl}
              onDisponibilidadChange={onDisponibilidadChange}
              onDelete={onDelete}
            />
          ))}
        </div>
      </div>
    </>
  );
};

export default IndependientesSection;
