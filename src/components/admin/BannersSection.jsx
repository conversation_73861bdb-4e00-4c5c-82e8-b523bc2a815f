// src/components/admin/BannersSection.jsx
import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import BannerList from '../BannerList'; // Assuming BannerList is in src/components/

const BannersSection = ({ banners, loading, error, onRefresh, onDeleteBanner }) => {
  const navigate = useNavigate();

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="w-16 h-16 relative animate-pulse-glow">
          <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#ff9800] animate-spin"></div>
          <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#f44336] animate-spin animate-reverse"></div>
        </div>
        <p className="mt-4 text-gray-400">Cargando banners...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-[#2d0a16] p-6 rounded-lg border border-red-800 mb-6">
        <h3 className="text-xl font-semibold text-red-400 mb-2">Error</h3>
        <p className="text-red-300">{error}</p>
      </div>
    );
  }

  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-[#ff9800] drop-shadow-sm flex items-center gap-2">
          <span className="text-[#f57c00]">🖼️</span> Gestión de Banners
        </h2>
        <Link
          to="/admin/nuevo-banner"
          className="px-6 py-3 bg-gradient-to-r from-[#ff9800] to-[#f44336] text-white rounded-lg hover:shadow-lg hover:shadow-[#ff9800]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 flex items-center gap-2 font-medium"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          <span>Nuevo Banner</span>
        </Link>
      </div>
      <BannerList
        banners={banners}
        onEdit={(banner) => navigate(`/admin/editar-banner/${banner.id}`)}
        onDelete={onDeleteBanner} // This will trigger the modal in AdminPage
        onRefresh={onRefresh}
      />
    </>
  );
};

export default BannersSection;
