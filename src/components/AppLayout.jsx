import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Componente que hace scroll al inicio de la página cuando cambia la ruta
 * Se usa como envoltorio en cada página
 */
const AppLayout = ({ children }) => {
  const { pathname } = useLocation();

  useEffect(() => {
    // Hacer scroll al inicio cuando cambia la ruta
    window.scrollTo(0, 0);
  }, [pathname]);

  return children;
};

export default AppLayout;
