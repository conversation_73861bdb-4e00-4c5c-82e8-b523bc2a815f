import { useState } from 'react';

const OfertaAccordion = ({ oferta }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  // Función para convertir saltos de línea a etiquetas <br>
  const convertNewlinesToBr = (text) => {
    if (!text) return '';

    // Reemplazar saltos de línea con etiquetas <br>
    return text.replace(/\n/g, '<br>');
  };

  // Función para sanitizar HTML permitiendo solo ciertas etiquetas
  const sanitizeHtml = (html) => {
    if (!html) return '';

    // Primero convertir saltos de línea a <br> si el contenido es texto plano
    // Solo hacemos esto si no detectamos etiquetas HTML
    if (typeof html === 'string' && !/<[a-z][\s\S]*>/i.test(html)) {
      html = convertNewlinesToBr(html);
    }

    // Crear un elemento temporal para trabajar con el HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Función recursiva para limpiar nodos
    const cleanNode = (node) => {
      // Lista de etiquetas permitidas
      const allowedTags = ['b', 'i', 'u', 'strong', 'em', 'br', 'p', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span'];
      // Lista de atributos permitidos
      const allowedAttrs = ['style', 'class'];

      // Si es un elemento
      if (node.nodeType === 1) {
        // Si la etiqueta no está permitida, reemplazarla con su contenido
        if (!allowedTags.includes(node.tagName.toLowerCase())) {
          // Crear un fragmento para contener los hijos
          const fragment = document.createDocumentFragment();
          // Mover todos los hijos al fragmento
          while (node.firstChild) {
            cleanNode(node.firstChild);
            fragment.appendChild(node.firstChild);
          }
          // Reemplazar el nodo con el fragmento
          node.parentNode.replaceChild(fragment, node);
          return;
        }

        // Eliminar todos los atributos excepto los permitidos
        const attrs = Array.from(node.attributes);
        attrs.forEach(attr => {
          if (!allowedAttrs.includes(attr.name.toLowerCase())) {
            node.removeAttribute(attr.name);
          }
        });

        // Limpiar estilos para permitir solo propiedades seguras
        if (node.style) {
          const allowedStyles = ['color', 'background-color', 'font-size', 'font-weight', 'text-align', 'text-decoration'];
          const styles = Array.from(node.style);
          styles.forEach(style => {
            if (!allowedStyles.includes(style)) {
              node.style.removeProperty(style);
            }
          });
        }

        // Limpiar recursivamente todos los hijos
        Array.from(node.childNodes).forEach(cleanNode);
      }
    };

    // Limpiar todos los nodos
    Array.from(tempDiv.childNodes).forEach(cleanNode);

    return tempDiv.innerHTML;
  };

  // Función para renderizar HTML de forma segura
  const createMarkup = (htmlContent) => {
    if (htmlContent === undefined || htmlContent === null) {
      return { __html: '' };
    }

    // Si es un objeto, convertirlo a string para evitar [object Object]
    if (typeof htmlContent === 'object') {
      try {
        htmlContent = JSON.stringify(htmlContent);
      } catch (e) {
        htmlContent = 'Error al mostrar contenido';
      }
    }

    return { __html: sanitizeHtml(String(htmlContent)) };
  };

  return (
    <div className="bg-[#1e1e2e] rounded-xl overflow-hidden shadow-lg border border-[#3d3d3d] mb-4">
      {/* Encabezado con gradiente - Ahora es el botón para expandir/contraer */}
      <button
        onClick={toggleAccordion}
        className="w-full bg-gradient-to-r from-[#9c27b0] to-[#e91e63] p-4 text-left flex justify-between items-center"
      >
        <h3 className="text-xl font-bold text-white">{oferta.titulo}</h3>
        <svg
          className={`w-5 h-5 text-white transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>

      {/* Contenido del acordeón - solo visible cuando isOpen es true */}
      {isOpen && (
        <div className="p-5 transition-all">
          {/* Intentar parsear la descripción como JSON */}
          {(() => {
            try {
              // Intentar parsear la descripción como JSON
              let secciones;

              // Verificar si ya es un objeto (puede ocurrir si ya se parseó antes)
              if (typeof oferta.descripcion === 'object' && Array.isArray(oferta.descripcion)) {
                secciones = oferta.descripcion;
              } else {
                secciones = JSON.parse(oferta.descripcion);
              }

              // Verificar si es un array válido
              if (Array.isArray(secciones) && secciones.length > 0) {
                return (
                  <div className="space-y-4">
                    {secciones.map((seccion, index) => {
                      // Verificar que la sección tenga la estructura esperada
                      if (!seccion || typeof seccion !== 'object') {
                        return null;
                      }

                      return (
                        <div key={index} className="mb-4">
                          {seccion.titulo && (
                            <h4 className="text-[#e0b3ff] font-semibold text-lg mb-2 border-b border-[#3d3d5a] pb-1">
                              {seccion.titulo}
                            </h4>
                          )}
                          <div
                            className="text-gray-200 text-base whitespace-pre-line"
                            dangerouslySetInnerHTML={createMarkup(seccion.descripcion)}
                          />
                        </div>
                      );
                    })}
                  </div>
                );
              }
            } catch (error) {
              // Si no se puede parsear como JSON, mostrar la descripción completa
              return (
                <div
                  className="text-gray-200 mb-3 text-base"
                  dangerouslySetInnerHTML={createMarkup(oferta.descripcion)}
                />
              );
            }

            // Si llegamos aquí, algo salió mal, mostrar la descripción original
            return (
              <div
                className="text-gray-200 mb-3 text-base whitespace-pre-line"
                dangerouslySetInnerHTML={createMarkup(oferta.descripcion)}
              />
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default OfertaAccordion;
