import React, { useState } from 'react';
import LazyImage from '../LazyImage';
import { Link } from 'react-router-dom';
import { API_URL } from '../../config';

const OfertaDiasBrujitasGrid = ({ modelosConDias }) => {
  const [isOpen, setIsOpen] = useState(true);

  // Definición de los días de la semana
  const daysConfig = [
    { key: 'lunes', label: 'Lunes' },
    { key: 'martes', label: 'Martes' },
    { key: 'miercoles', label: 'Miércoles' },
    { key: 'jueves', label: 'Jueves' },
    { key: 'viernes', label: 'Viernes' },
    { key: 'sabado', label: 'Sábado' },
    { key: 'domingo', label: 'Domingo' },
  ];

  if (!modelosConDias || modelosConDias.length === 0) {
    return null; // No renderizar nada si no hay brujitas para esta oferta
  }

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  // Organizar brujitas por día de la semana
  const brujitasPorDia = {};

  // Inicializar el objeto con arrays vacíos para cada día
  daysConfig.forEach(day => {
    brujitasPorDia[day.key] = [];
  });

  // Agrupar brujitas por día
  modelosConDias.forEach(modelo => {
    const diasSeleccionados = modelo.diasSeleccionados || [];

    diasSeleccionados.forEach(dia => {
      if (brujitasPorDia[dia]) {
        brujitasPorDia[dia].push(modelo);
      }
    });
  });

  return (
    <div className="mb-6">
      <button
        type="button"
        onClick={toggleAccordion}
        className="w-full flex items-center justify-between p-3 bg-[#23233a]/60 rounded-lg border border-[#3d3d3d] text-[#e0b3ff] hover:bg-[#29293d]/80 transition-all shadow-md"
      >
        <div className="flex items-center gap-2">
          <span className="text-[#e91e63] text-lg">🫦</span>
          <span className="font-semibold text-md">Días disponibles con oferta</span>
        </div>
        <div className="flex items-center gap-2">
          <svg
            className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </button>

      {isOpen && (
        <div className="mt-3 bg-[#23233a]/40 p-4 rounded-lg border border-[#3d3d3d] shadow-md overflow-hidden transition-all">
          {/* Mensaje informativo */}
          <p className="text-gray-300 text-sm mb-4 italic">
            Haz clic en la foto de una brujita para ver su perfil completo y solicitar esta oferta.
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {daysConfig.map(day => {
              const brujitasDelDia = brujitasPorDia[day.key] || [];

              // Si no hay brujitas para este día, no mostrar el día
              if (brujitasDelDia.length === 0) {
                return null;
              }

              return (
                <div
                  key={day.key}
                  className="bg-[#29293d]/50 rounded-lg border border-[#3d3d3d] overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                >
                  {/* Encabezado del día */}
                  <div className="bg-gradient-to-r from-[#9c27b0] to-[#e91e63] p-2.5 text-center">
                    <h3 className="text-white font-semibold text-lg">{day.label}</h3>
                  </div>

                  {/* Brujitas disponibles este día */}
                  <div className="p-3 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 gap-3">
                    {brujitasDelDia.map(brujita => (
                      <Link
                        key={`${day.key}-${brujita.id}`}
                        to={brujita.collectionId === 'independientes' ? `/independiente/${brujita.id}` : `/brujita/${brujita.id}`}
                        className="flex flex-col items-center p-2 rounded-md hover:bg-[#31314a]/70 transition-all group"
                      >
                        {/* Foto de perfil */}
                        <div className="w-14 h-14 rounded-full overflow-hidden border-2 border-[#9c27b0]/80 flex-shrink-0 group-hover:border-[#e91e63] transition-all shadow-md group-hover:shadow-[0_0_10px_rgba(233,30,99,0.5)]">
                          {brujita.imagen_perfil ? (
                            <LazyImage
                              src={`${API_URL}/api/files/${brujita.collectionId || 'brujitas'}/${brujita.id}/${brujita.imagen_perfil}`}
                              alt={brujita.nombre}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-[#2d1949]/70 flex items-center justify-center text-white text-xs">
                              Sin foto
                            </div>
                          )}
                        </div>

                        {/* Nombre */}
                        <span className="text-white text-xs font-medium mt-2 text-center group-hover:text-[#e91e63] transition-colors line-clamp-1 max-w-full">
                          {brujita.nombre}
                        </span>
                      </Link>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default OfertaDiasBrujitasGrid;
