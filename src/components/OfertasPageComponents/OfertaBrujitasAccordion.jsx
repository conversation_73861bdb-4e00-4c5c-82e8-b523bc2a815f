import React, { useState } from 'react';
import LazyImage from '../LazyImage';
import { Link } from 'react-router-dom';
import { API_URL } from '../../config';

const OfertaBrujitasAccordion = ({ modelosConDias }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedModelo, setExpandedModelo] = useState(null);
  
  const allDaysLabels = {
    lunes: 'Lunes',
    martes: 'Martes',
    miercoles: 'Miércoles',
    jueves: 'Jueves',
    viernes: 'Viernes',
    sabado: 'Sábado',
    domingo: 'Domingo',
  };

  if (!modelosConDias || modelosConDias.length === 0) {
    return null; // No renderizar nada si no hay brujitas para esta oferta
  }

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
    if (isOpen) { // Si se está cerrando el acordeón principal
      setExpandedModelo(null); // Cerrar cualquier modelo expandido
    }
  };

  const toggleModelo = (modeloId) => {
    setExpandedModelo(expandedModelo === modeloId ? null : modeloId);
  };

  return (
    <div className="mb-6">
      <button
        type="button"
        onClick={toggleAccordion}
        className="w-full flex items-center justify-between p-3 bg-[#23233a]/60 rounded-lg border border-[#3d3d3d] text-[#e0b3ff] hover:bg-[#29293d]/80 transition-all shadow-md"
      >
        <div className="flex items-center gap-2">
          <span className="text-[#e91e63] text-lg">🫦</span>
          <span className="font-semibold text-md">Días validos de oferta de cada Brujita</span>
        </div>
        <div className="flex items-center gap-2">
          <svg
            className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </button>

      {isOpen && (
        <div className="mt-3 bg-[#23233a]/40 p-4 rounded-lg border border-[#3d3d3d] shadow-md overflow-hidden transition-all">
          <div className="space-y-3">
            {modelosConDias.map(modelo => {
              const diasSeleccionados = modelo.diasSeleccionados || [];
              const diasActivos = diasSeleccionados.map(diaKey => allDaysLabels[diaKey]).filter(Boolean);

              return (
                <div key={modelo.id} className="border border-[#3d3d3d]/70 rounded-md bg-[#29293d]/50 overflow-hidden hover:border-[#9c27b0]/70 transition-colors">
                  <div className="w-full flex justify-between items-center p-2.5">
                    <Link to={`/brujita/${modelo.id}`} className="flex items-center gap-2.5 group">
                      <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-[#9c27b0]/80 flex-shrink-0 group-hover:border-[#e91e63] transition-colors">
                        {modelo.imagen_perfil ? (
                          <LazyImage
                            src={`${API_URL}/api/files/brujitas/${modelo.id}/${modelo.imagen_perfil}`}
                            alt={modelo.nombre}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-[#2d1949]/70 flex items-center justify-center text-white text-xs">
                            Sin foto
                          </div>
                        )}
                      </div>
                      <span className="text-white text-sm font-medium group-hover:text-[#e91e63] transition-colors">
                        {modelo.nombre}
                      </span>
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-gray-400 group-hover:text-[#e91e63] transition-colors opacity-0 group-hover:opacity-100">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                      </svg>
                    </Link>
                    <button
                      onClick={() => toggleModelo(modelo.id)}
                      className="p-1 hover:bg-[#31314a]/50 rounded-md transition-colors" // Added padding and rounded for better click target
                    >
                      <svg
                        className={`w-4 h-4 transition-transform text-gray-400 ${expandedModelo === modelo.id ? 'transform rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </button>
                  </div>
                  
                  {expandedModelo === modelo.id && (
                    <div className="p-2.5 pt-2 border-t border-[#3d3d3d]/70"> {/* Adjusted padding top */}
                      <div className="mt-0"> {/* Removed margin top */}
                        <div className="text-xs text-gray-300 mb-1.5">Días con oferta disponible:</div>
                        <div className="flex flex-wrap gap-1.5">
                          {diasActivos.length > 0 ? (
                            diasActivos.map(diaLabel => (
                              <span 
                                key={diaLabel} 
                                className="bg-[#9c27b0]/80 text-white px-2.5 py-0.5 rounded-full text-xs flex items-center"
                              >
                                <span className="mr-1">✓</span>
                                {diaLabel}
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-400 text-xs italic">No hay días específicos para esta oferta.</span>
                          )}
                        </div>
                      </div>
                      {/* "Ver perfil completo" link removed from here */}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default OfertaBrujitasAccordion;
