import React from 'react';
import LazyImage from '../LazyImage';
import { Link } from 'react-router-dom';
import { API_URL } from '../../config';

const DiasBrujitasOfertasGrid = ({ ofertas }) => {
  // Definición de los días de la semana
  const daysConfig = [
    { key: 'lunes', label: 'Lunes' },
    { key: 'martes', label: '<PERSON><PERSON>' },
    { key: 'miercoles', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { key: 'jueves', label: 'Jueves' },
    { key: 'viernes', label: 'Viernes' },
    { key: 'sabado', label: 'Sábado' },
    { key: 'domingo', label: 'Domingo' },
  ];

  if (!ofertas || ofertas.length === 0) {
    return null;
  }

  // Organizar brujitas por día de la semana y oferta
  const brujitasPorDia = {};

  // Inicializar el objeto con arrays vacíos para cada día
  daysConfig.forEach(day => {
    brujitasPorDia[day.key] = [];
  });

  // Recorrer todas las ofertas y agrupar brujitas por día
  ofertas.forEach(oferta => {
    const brujitasConDias = oferta.brujitasConDias || [];

    brujitasConDias.forEach(brujita => {
      const diasSeleccionados = brujita.diasSeleccionados || [];

      diasSeleccionados.forEach(dia => {
        if (brujitasPorDia[dia]) {
          // Agregar la brujita con información de la oferta
          brujitasPorDia[dia].push({
            ...brujita,
            oferta: {
              id: oferta.id,
              titulo: oferta.titulo
            }
          });
        }
      });
    });
  });

  return (
    <div className="mb-8">
      <h2 className="text-2xl font-bold text-white text-center mb-6">
        <span className="mr-2">📅</span> Días de las Brujitas con Ofertas
      </h2>

      <div className="bg-[#23233a]/40 p-4 rounded-lg border border-[#3d3d3d] shadow-md overflow-hidden">
        {/* Mensaje informativo */}
        <p className="text-gray-300 text-sm mb-6 italic text-center">
          Haz clic en la foto de una brujita para ver su perfil completo.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Días alternados en dos columnas */}
          {daysConfig.slice(0, 6).map((day, index) => {
            const brujitasDelDia = brujitasPorDia[day.key] || [];

            // Si no hay brujitas para este día, no mostrar el día
            if (brujitasDelDia.length === 0) {
              return null;
            }

            // Determinar si va en columna izquierda o derecha
            const isEven = index % 2 === 0;
            const columnClass = `md:col-span-1 ${isEven ? 'md:col-start-1' : 'md:col-start-2'}`;

            return (
              <div
                key={day.key}
                className={`bg-[#29293d]/50 rounded-lg border border-[#3d3d3d] overflow-hidden shadow-lg ${columnClass}`}
              >
                {/* Encabezado del día */}
                <div className="bg-gradient-to-r from-[#9c27b0] to-[#e91e63] p-3 text-center">
                  <h3 className="text-white font-semibold text-xl">{day.label}</h3>
                </div>

                {/* Brujitas disponibles este día */}
                <div className="p-4 space-y-3">
                  <div className={`grid ${brujitasDelDia.length === 1 ? 'grid-cols-1' : brujitasDelDia.length === 2 ? 'grid-cols-2' : 'grid-cols-3'} gap-2 justify-items-center`}>
                    {brujitasDelDia.map(brujita => (
                      <Link
                        key={`${day.key}-${brujita.id}-${brujita.oferta.id}`}
                        to={brujita.collectionId === 'independientes' ? `/independiente/${brujita.id}` : `/brujita/${brujita.id}`}
                        className="flex flex-col items-center p-2 rounded-lg hover:bg-[#31314a]/70 transition-all group w-full max-w-[120px]"
                      >
                        {/* Foto de perfil - Centrada y más grande */}
                        <div className="w-16 h-16 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-full overflow-hidden border-2 border-[#9c27b0]/80 group-hover:border-[#e91e63] transition-all shadow-md group-hover:shadow-[0_0_10px_rgba(233,30,99,0.5)] mb-2">
                          {brujita.imagen_perfil ? (
                            <LazyImage
                              src={`${API_URL}/api/files/${brujita.collectionId || 'brujitas'}/${brujita.id}/${brujita.imagen_perfil}`}
                              alt={brujita.nombre}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-[#2d1949]/70 flex items-center justify-center text-white text-xs">
                              Sin foto
                            </div>
                          )}
                        </div>

                        {/* Nombre centrado debajo */}
                        <span className="text-white text-sm font-medium group-hover:text-[#e91e63] transition-colors text-center line-clamp-1 w-full">
                          {brujita.nombre}
                        </span>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}

          {/* Domingo (ocupa todo el ancho) */}
          {(() => {
            const domingo = daysConfig[6]; // Domingo es el último día
            const brujitasDomingo = brujitasPorDia[domingo.key] || [];

            // Si no hay brujitas para el domingo, no mostrar el día
            if (brujitasDomingo.length === 0) {
              return null;
            }

            return (
              <div
                key={domingo.key}
                className="bg-[#29293d]/50 rounded-lg border border-[#3d3d3d] overflow-hidden shadow-lg col-span-1 md:col-span-2"
              >
                {/* Encabezado del día */}
                <div className="bg-gradient-to-r from-[#9c27b0] to-[#e91e63] p-3 text-center">
                  <h3 className="text-white font-semibold text-xl">{domingo.label}</h3>
                </div>

                {/* Brujitas disponibles este día */}
                <div className="p-4">
                  <div className={`grid ${brujitasDomingo.length === 1 ? 'grid-cols-1' : brujitasDomingo.length === 2 ? 'grid-cols-2' : brujitasDomingo.length === 3 ? 'grid-cols-3' : 'grid-cols-4'} gap-3 justify-items-center`}>
                    {brujitasDomingo.map(brujita => (
                      <Link
                        key={`${domingo.key}-${brujita.id}-${brujita.oferta.id}`}
                        to={brujita.collectionId === 'independientes' ? `/independiente/${brujita.id}` : `/brujita/${brujita.id}`}
                        className="flex flex-col items-center p-3 rounded-lg hover:bg-[#31314a]/70 transition-all group"
                      >
                        {/* Foto de perfil - Centrada y más grande */}
                        <div className="w-16 h-16 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-full overflow-hidden border-2 border-[#9c27b0]/80 group-hover:border-[#e91e63] transition-all shadow-md group-hover:shadow-[0_0_10px_rgba(233,30,99,0.5)] mb-2">
                          {brujita.imagen_perfil ? (
                            <LazyImage
                              src={`${API_URL}/api/files/${brujita.collectionId || 'brujitas'}/${brujita.id}/${brujita.imagen_perfil}`}
                              alt={brujita.nombre}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-[#2d1949]/70 flex items-center justify-center text-white text-xs">
                              Sin foto
                            </div>
                          )}
                        </div>

                        {/* Nombre centrado debajo */}
                        <span className="text-white text-sm font-medium group-hover:text-[#e91e63] transition-colors text-center line-clamp-1 w-full">
                          {brujita.nombre}
                        </span>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      </div>
    </div>
  );
};

export default DiasBrujitasOfertasGrid;
