import React from 'react';
import LazyImage from './LazyImage';

const CarouselSlide = ({ slide, isActive }) => {
  return (
    <div
      className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${isActive ? 'opacity-100 z-10' : 'opacity-0 z-0'}`}
    >
      {/* Fondo del slide con imagen usando LazyImage */}
      <div className="absolute inset-0">
        <LazyImage
          src={slide.bgImage}
          alt={slide.title || "Banner"}
          className="w-full h-full"
          objectFit="cover"
        />
      </div>

      {/* Overlay para mejorar legibilidad del texto */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#000000]/0 to-[#000000]/80"></div>



      {/* Contenido del slide */}
      <div className="relative h-full z-20 flex flex-col justify-end pb-8 px-12 md:px-12 lg:px-20">
        <div className="max-w-3xl">
          <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold text-white mb-3 animate-fade-in">
            {slide.title} {slide.subtitle && (
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff9ff3] to-[#feca57]">
                {slide.subtitle}
              </span>
            )}
          </h1>
          {slide.description && (
            <p className="text-sm md:text-base text-gray-200 max-w-xl mb-5 animate-slide-up">
              {slide.description}
            </p>
          )}

          {/* Contenedor de botones con lógica para ajustar el ancho */}
          <div className="grid grid-cols-1 gap-3 w-full max-w-full sm:max-w-xl">
            {(() => {
              // Determinar cuántos botones hay
              const hasWhatsApp = slide.buttonText && slide.buttonLink;
              const hasTelegram = slide.telegramText && slide.telegramLink;
              const hasCustom = slide.customText && slide.customLink;

              const buttonCount = [hasWhatsApp, hasTelegram, hasCustom].filter(Boolean).length;

              // Actualizar el grid basado en la cantidad de botones
              if (buttonCount >= 2) {
                // Cuando hay exactamente 2 botones, siempre mostrarlos en la misma línea
                // Para 3 o más, usamos responsive (apilados en móvil, 2 por línea en desktop)
                const gridClass = buttonCount === 2
                  ? "grid grid-cols-2 gap-3 w-full" // Siempre 2 columnas para 2 botones
                  : "grid grid-cols-1 sm:grid-cols-2 gap-3 w-full"; // Responsive para 3+ botones

                return (
                  <div className={gridClass}>
                    {/* Botón de WhatsApp */}
                    {hasWhatsApp && (
                      <a
                        href={slide.buttonLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex justify-center items-center px-3 sm:px-5 py-2 bg-gradient-to-r from-[#25D366] to-[#128C7E] text-white font-medium rounded-lg hover:shadow-lg hover:shadow-[#25D366]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 text-sm sm:text-base w-full"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
                        </svg>
                        <span className="truncate">{slide.buttonText}</span>
                      </a>
                    )}

                    {/* Botón de Telegram */}
                    {hasTelegram && (
                      <a
                        href={slide.telegramLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex justify-center items-center px-3 sm:px-5 py-2 bg-gradient-to-r from-[#0088cc] to-[#29B6F6] text-white font-medium rounded-lg hover:shadow-lg hover:shadow-[#0088cc]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 text-sm sm:text-base w-full"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                        </svg>
                        <span className="truncate">{slide.telegramText}</span>
                      </a>
                    )}

                    {/* Botón Personalizado */}
                    {hasCustom && (
                      <a
                        href={slide.customLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex justify-center items-center px-3 sm:px-5 py-2 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white font-medium rounded-lg hover:shadow-lg hover:shadow-[#9c27b0]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 text-sm sm:text-base w-full"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
                        </svg>
                        <span className="truncate">{slide.customText}</span>
                      </a>
                    )}
                  </div>
                );
              } else {
                // Si solo hay un botón, mostrar a ancho completo
                return (
                  <>
                    {/* Botón de WhatsApp */}
                    {hasWhatsApp && (
                      <a
                        href={slide.buttonLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex justify-center items-center px-3 sm:px-5 py-2 bg-gradient-to-r from-[#25D366] to-[#128C7E] text-white font-medium rounded-lg hover:shadow-lg hover:shadow-[#25D366]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 text-sm sm:text-base w-full"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
                        </svg>
                        <span>{slide.buttonText}</span>
                      </a>
                    )}

                    {/* Botón de Telegram */}
                    {hasTelegram && (
                      <a
                        href={slide.telegramLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex justify-center items-center px-3 sm:px-5 py-2 bg-gradient-to-r from-[#0088cc] to-[#29B6F6] text-white font-medium rounded-lg hover:shadow-lg hover:shadow-[#0088cc]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 text-sm sm:text-base w-full"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                        </svg>
                        <span>{slide.telegramText}</span>
                      </a>
                    )}

                    {/* Botón Personalizado */}
                    {hasCustom && (
                      <a
                        href={slide.customLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex justify-center items-center px-3 sm:px-5 py-2 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white font-medium rounded-lg hover:shadow-lg hover:shadow-[#9c27b0]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all duration-300 text-sm sm:text-base w-full"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
                        </svg>
                        <span>{slide.customText}</span>
                      </a>
                    )}
                  </>
                );
              }
            })()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CarouselSlide;
