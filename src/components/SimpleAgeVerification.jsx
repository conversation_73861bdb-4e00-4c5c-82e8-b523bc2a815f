import React from 'react';

const SimpleAgeVerification = ({ onAccept, onReject }) => {
  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4 bg-black bg-opacity-80" style={{ backdropFilter: 'blur(5px)' }}>
      <div className="bg-[#1e1e2e] rounded-xl shadow-2xl max-w-md w-full overflow-hidden border border-[#3d3d3d]">
        {/* Header */}
        <div className="p-4 border-b border-[#3d3d3d] bg-gradient-to-r from-[#4a148c] to-[#880e4f]">
          <h2 className="text-xl font-bold text-white flex items-center">
            <span className="mr-2">⚠️</span> Verificación de Edad
          </h2>
        </div>

        {/* Content */}
        <div className="p-6 text-gray-300 space-y-4">
          <div className="bg-[#2d1949]/30 p-4 rounded-lg border border-[#3d3d3d] mb-4">
            <p className="text-lg font-semibold text-pink-300 mb-2 flex items-center">
              <span className="mr-2">🔞</span> Confirmación de Edad
            </p>
            <p>
              Este sitio contiene material para adultos. Al acceder, confirmas que tienes al menos 18 años de edad.
            </p>
          </div>
          
          <p className="text-sm text-gray-400 italic">
            Al hacer clic en "Acepto", confirmas que tienes al menos 18 años de edad.
          </p>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-[#3d3d3d] bg-[#1a1a2e] flex flex-col sm:flex-row justify-end gap-3">
          <button
            onClick={onReject}
            className="px-4 py-2 bg-[#2d2d2d] text-gray-300 rounded-lg hover:bg-[#3d3d3d] transition-colors duration-300 font-medium"
          >
            Rechazar y Salir
          </button>

          <button
            onClick={onAccept}
            className="px-4 py-2 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-lg hover:shadow-lg hover:shadow-[#9c27b0]/20 transition-all duration-300 font-medium"
          >
            Acepto
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleAgeVerification;
