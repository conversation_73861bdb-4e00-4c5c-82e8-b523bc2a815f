import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { independientesService } from '../services/independientesService';
import logger from '../services/logService';
import BasicInfoSection from './IndependienteFormComponents/BasicInfoSection';
import ZonaTrabajoSection from './IndependienteFormComponents/ZonaTrabajoSection';
import PersonalInfoSection from './IndependienteFormComponents/PersonalInfoSection';
import ImageUploadSection from './IndependienteFormComponents/ImageUploadSection';
import FormActions from './IndependienteFormComponents/FormActions';

const IndependienteForm = ({ modelo = null, onSubmit }) => {
  // Renombrar la variable para mayor claridad
  const independiente = modelo;
  const navigate = useNavigate();
  const isEditing = !!independiente; // Use independiente prop

  // Estado inicial del formulario
  const [formData, setFormData] = useState({
    nombre: '',
    edad: '',
    perfil_corporal: '',
    destacada: false,
    vip: false,
    disponibilidad: 'Disponible', // Por defecto está disponible
    estado: [],
    biografia: '',
    zonas: [],
    altura: '',
    imagen_perfil: null,
    galeria: []
  });

  // Estado para manejar las previsualizaciones de imágenes
  const [imagePreviews, setImagePreviews] = useState({
    imagen_perfil: null,
    galeria: []
  });

  // Estado para manejar errores
  const [errors, setErrors] = useState({});
  // Estado para manejar el envío del formulario
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Cargar datos de la independiente si estamos editando
  useEffect(() => {
    if (isEditing && independiente) {
      // Procesar el campo estado
      let estadoArray = [];
      if (independiente.estado) {
        if (typeof independiente.estado === 'string') {
          // Si es un string, dividirlo por comas
          estadoArray = independiente.estado.split(',').map(item => item.trim());
        } else if (Array.isArray(independiente.estado)) {
          // Si ya es un array, usarlo directamente
          estadoArray = independiente.estado;
        }
      }

      // Verificar si VIP o Destacada están en el array de estado
      const isVIP = estadoArray.includes('VIP');
      const isDestacada = estadoArray.includes('Destacada');

      // Preparar las previsualizaciones de imágenes existentes
      const newImagePreviews = { imagen_perfil: null, galeria: [] };

      if (independiente.imagen_perfil) {
        newImagePreviews.imagen_perfil = independientesService.getImageUrl(independiente, independiente.imagen_perfil);
      }

      if (independiente.galeria && independiente.galeria.length > 0) {
        newImagePreviews.galeria = independiente.galeria.map(img => ({
          url: independientesService.getImageUrl(independiente, img),
          filename: img
        }));
      }

      // Actualizar las previsualizaciones
      setImagePreviews(newImagePreviews);

      // Procesar zonas de trabajo
      let zonasArray = [];
      if (independiente.zonas) {
        try {
          // Si es un string, intentar parsearlo como JSON
          if (typeof independiente.zonas === 'string') {
            zonasArray = JSON.parse(independiente.zonas);
          }
          // Si ya es un array, usarlo directamente
          else if (Array.isArray(independiente.zonas)) {
            zonasArray = independiente.zonas;
          }
        } catch (error) {
          logger.error('Error al parsear zonas:', error);
        }
      }

      // Determinar el estado de disponibilidad
      let disponibilidadValue = 'Disponible'; // Valor por defecto

      if (independiente.disponibilidad) {
        // Si existe el campo disponibilidad, usarlo
        disponibilidadValue = independiente.disponibilidad;
      } else if (independiente.disponible !== undefined) {
        // Si no existe disponibilidad pero sí disponible, convertir el booleano
        disponibilidadValue = independiente.disponible ? 'Disponible' : 'No disponible';
      }

      setFormData({
        nombre: independiente.nombre || '',
        edad: independiente.edad || '',
        perfil_corporal: independiente.perfil_corporal || '',
        destacada: isDestacada || independiente.destacada || false,
        vip: isVIP || independiente.vip || false,
        disponibilidad: disponibilidadValue,
        estado: estadoArray,
        biografia: independiente.biografia || '',
        zonas: zonasArray,
        altura: independiente.altura || '',
        imagen_perfil: null,
        galeria: []
      });
    }
  }, [independiente, isEditing]);

  // Manejar cambios en los campos del formulario
  const handleChange = (e) => {
    const { name, value, type, files } = e.target;

    if (type === 'file') {
      if (name === 'galeria') {
        // Para múltiples archivos
        const fileArray = Array.from(files);
        setFormData(prev => ({
          ...prev,
          [name]: fileArray
        }));

        // Crear previsualizaciones para la galería
        const newPreviews = fileArray.map(file => ({
          url: URL.createObjectURL(file),
          file: file
        }));

        setImagePreviews(prev => ({
          ...prev,
          [name]: [...prev[name], ...newPreviews]
        }));
      } else {
        // Para un solo archivo
        const file = files[0];
        if (file) {
          setFormData(prev => ({
            ...prev,
            [name]: file
          }));

          // Crear previsualización para la imagen
          setImagePreviews(prev => ({
            ...prev,
            [name]: URL.createObjectURL(file)
          }));
        }
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Función para eliminar una imagen
  const handleRemoveImage = (type, index = null) => {
    if (type === 'galeria' && index !== null) {
      // Eliminar una imagen de la galería
      setImagePreviews(prev => ({
        ...prev,
        galeria: prev.galeria.filter((_, i) => i !== index)
      }));

      setFormData(prev => ({
        ...prev,
        galeria: prev.galeria.filter((_, i) => i !== index)
      }));
    } else {
      // Eliminar imagen destacada o de perfil
      setImagePreviews(prev => ({
        ...prev,
        [type]: null
      }));

      setFormData(prev => ({
        ...prev,
        [type]: null
      }));
    }
  };

  // Manejar cambios en los switches Destacada, VIP y Disponibilidad
  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;

    // Actualizar el estado normal
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));

    // Actualizar el campo estado para VIP y Destacada
    if (name === 'vip' || name === 'destacada') {
      const estadoValue = name === 'vip' ? 'VIP' : 'Destacada';

      setFormData(prev => {
        let newEstado = [...prev.estado];

        if (checked && !newEstado.includes(estadoValue)) {
          // Agregar al array si está marcado y no existe
          newEstado.push(estadoValue);
        } else if (!checked && newEstado.includes(estadoValue)) {
          // Quitar del array si no está marcado y existe
          newEstado = newEstado.filter(item => item !== estadoValue);
        }

        return {
          ...prev,
          estado: newEstado
        };
      });
    }
  };

  // Manejar cambios en la disponibilidad
  const toggleDisponibilidad = () => {
    setFormData(prev => ({
      ...prev,
      disponibilidad: prev.disponibilidad === 'Disponible' ? 'No disponible' : 'Disponible'
    }));
  };

  // Función para obtener el color de fondo según el estado
  const getStatusColor = (status) => {
    if (status === 'vip') return 'bg-gradient-to-r from-[#e91e63] to-[#9c27b0]';
    if (status === 'destacada') return 'bg-gradient-to-r from-[#ff9800] to-[#f44336]';
    if (status === 'disponible') return 'bg-[#4CAF50]';
    return 'bg-[#29293d]';
  };

  // Validar el formulario
  const validateForm = () => {
    const newErrors = {};

    if (!formData.nombre) newErrors.nombre = 'El nombre es obligatorio';
    if (!formData.edad) newErrors.edad = 'La edad es obligatoria';
    if (!formData.perfil_corporal) newErrors.perfil_corporal = 'El perfil corporal es obligatorio';

    // Si no estamos editando, la imagen de perfil es obligatoria
    if (!isEditing && !formData.imagen_perfil) {
      newErrors.imagen_perfil = 'La imagen de perfil es obligatoria';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manejar envío del formulario
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Preparar el campo estado basado en vip y destacada
      let estadoArray = [];
      if (formData.vip) estadoArray.push('VIP');
      if (formData.destacada) estadoArray.push('Destacada');

      // Crear un nuevo objeto FormData para enviar archivos
      const formDataToSend = new FormData();

      // Agregar el resto de campos
      formDataToSend.append('nombre', formData.nombre);
      formDataToSend.append('edad', formData.edad);
      formDataToSend.append('perfil_corporal', formData.perfil_corporal);
      formDataToSend.append('altura', formData.altura);
      formDataToSend.append('biografia', formData.biografia);

      // Agregar disponibilidad (esto es lo único que usaremos para el estado de disponibilidad)
      formDataToSend.append('disponibilidad', formData.disponibilidad);

      // Convertir disponibilidad a booleano para el campo disponible
      formDataToSend.append('disponible', formData.disponibilidad === 'Disponible');

      // Convertir zonas de trabajo a JSON
      formDataToSend.append('zonas', JSON.stringify(formData.zonas || []));

      // Agregar el campo estado directamente
      // PocketBase espera un array para campos select múltiples
      // Pero FormData convierte arrays a strings, así que necesitamos enviar cada valor por separado
      if (estadoArray.length > 0) {
        // Usar el mismo nombre de campo múltiples veces para crear un array en el servidor
        estadoArray.forEach(value => {
          formDataToSend.append('estado', value);
        });
      } else {
        // Si no hay estado seleccionado, enviamos un string vacío
        formDataToSend.append('estado', '');
      }

      // Agregar imagen de perfil si se ha seleccionado
      if (formData.imagen_perfil) {
        logger.debug('Agregando imagen perfil:', formData.imagen_perfil);
        formDataToSend.append('imagen_perfil', formData.imagen_perfil);
      }

      // Agregar múltiples archivos para la galería
      if (formData.galeria && formData.galeria.length > 0) {
        logger.debug('Agregando galería:', formData.galeria);
        formData.galeria.forEach((file, index) => {
          logger.debug(`Agregando imagen ${index}:`, file);
          formDataToSend.append('galeria', file);
        });
      }

      // Registrar campos que se van a enviar (solo en modo debug)
      logger.debug('Campos a enviar:');
      for (let [key, value] of formDataToSend.entries()) {
        if (value instanceof File) {
          logger.debug(`${key}: [File: ${value.name}]`);
        } else {
          logger.debug(`${key}: ${value}`);
        }
      }

      // Registrar los datos que se van a enviar (solo en modo debug)
      const dataToSend = Object.fromEntries(formDataToSend.entries());
      logger.debug('Datos a enviar:', dataToSend);
      logger.debug('Estado:', dataToSend.estado, typeof dataToSend.estado);

      let result;
      if (isEditing) {
        result = await independientesService.update(independiente.id, formDataToSend);
      } else {
        result = await independientesService.create(formDataToSend);
      }

      if (onSubmit) {
        onSubmit(result);
      }

      // Redirigir a la página de administración con la pestaña de independientes activa
      navigate('/admin?tab=independientes');
    } catch (error) {
      logger.error('Error al guardar independiente:', error);

      // Mostrar detalles del error si están disponibles
      if (error.data && error.data.data) {
        logger.error('Detalles del error:', error.data.data);

        // Mostrar el error completo para depuración
        logger.error('Error completo:', JSON.stringify(error, null, 2));

        // Mostrar errores específicos de campos
        const fieldErrors = error.data.data;
        Object.keys(fieldErrors).forEach(field => {
          logger.error(`Error en campo ${field}:`, fieldErrors[field]);
          setErrors(prev => ({
            ...prev,
            [field]: fieldErrors[field].message || JSON.stringify(fieldErrors[field])
          }));
        });
      } else if (error.originalError && error.originalError.data) {
        // Intentar extraer información de error de PocketBase
        logger.error('Error de PocketBase:', error.originalError.data);

        if (error.originalError.data.data) {
          const pbErrors = error.originalError.data.data;
          Object.keys(pbErrors).forEach(field => {
            logger.error(`Error en campo ${field}:`, pbErrors[field]);
            setErrors(prev => ({
              ...prev,
              [field]: pbErrors[field].message || JSON.stringify(pbErrors[field])
            }));
          });
        } else {
          setErrors({ submit: error.message || 'Error al guardar la independiente. Inténtalo de nuevo.' });
        }
      } else {
        logger.error('Error sin detalles:', error);
        setErrors({ submit: 'Error al guardar la independiente. Inténtalo de nuevo.' });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="bg-[#181828] p-8 rounded-2xl shadow-xl border border-[#29293d] max-w-2xl mx-auto">
        {/* Mostrar errores generales del formulario */}
        {errors.submit && (
          <div className="mb-6 p-4 bg-red-900/30 border border-red-800 rounded-lg">
            <p className="text-red-400 text-sm">{errors.submit}</p>
          </div>
        )}

        <BasicInfoSection
          formData={formData}
          errors={errors}
          handleChange={handleChange}
          handleSwitchChange={handleSwitchChange}
          toggleDisponibilidad={toggleDisponibilidad}
        />

        <ZonaTrabajoSection
          formData={formData}
          setFormData={setFormData}
        />

        <PersonalInfoSection
          formData={formData}
          handleChange={handleChange}
        />

        <ImageUploadSection
          imagePreviews={imagePreviews}
          errors={errors}
          handleChange={handleChange}
          handleRemoveImage={handleRemoveImage}
          isEditing={isEditing}
        />

        <FormActions
          isSubmitting={isSubmitting}
          isEditing={isEditing}
          errors={errors}
        />
      </div>
    </form>
  );
};

export default IndependienteForm; // Update export name
