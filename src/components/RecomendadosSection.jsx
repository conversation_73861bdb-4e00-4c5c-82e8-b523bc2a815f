import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import recomendadosService from '../services/recomendadosService';
import { modelosService } from '../services/pocketbase';
import { independientesService } from '../services/independientesService';
import LazyImage from './LazyImage';
import logger from '../services/logService';

/**
 * Mezcla aleatoriamente un array (algorit<PERSON> Fisher-Yates)
 * @param {Array} array - Array a mezclar
 * @returns {Array} - Array mezclado
 */
function shuffleArray(array) {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

const RecomendadosSection = ({ currentModelId, modelType, excludeTypes = [], limit = 3 }) => {
  const [recomendados, setRecomendados] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hoveredId, setHoveredId] = useState(null);

  useEffect(() => {
    const fetchRecomendados = async () => {
      try {
        setLoading(true);
        // Obtener recomendaciones del mismo tipo que el modelo actual
        const data = await recomendadosService.getRecomendados(
          currentModelId,
          modelType,
          limit,
          excludeTypes
        );

        // Verificar si tenemos menos recomendaciones de las solicitadas
        if (data.length < limit) {
          logger.debug(`Solo se obtuvieron ${data.length} recomendaciones de las ${limit} solicitadas`);
          
          // Determinar el tipo complementario
          const complementaryType = modelType === 'brujita' ? 'independiente' : 'brujita';
          
          // Verificar si el tipo complementario no está excluido
          if (!excludeTypes.includes(complementaryType)) {
            logger.debug(`Intentando complementar con ${complementaryType}s`);
            
            // Obtener modelos complementarios
            // Importante: No excluir ningún tipo para maximizar resultados
            const extraData = await recomendadosService.getRecomendados(
              currentModelId,
              complementaryType,
              // Solicitar más modelos de los necesarios para tener margen
              limit * 2,
              [] // No excluir ningún tipo para asegurar suficientes recomendaciones
            );
            
            logger.debug(`Se obtuvieron ${extraData.length} ${complementaryType}s complementarias`);
            
            // Combinar los resultados y asegurar que tenemos exactamente 'limit' recomendaciones
            // Primero mezclamos los datos complementarios para tener variedad
            const shuffledExtraData = shuffleArray(extraData);
            
            // Calcular cuántos modelos complementarios necesitamos
            const neededComplement = limit - data.length;
            
            // Tomar solo los complementarios necesarios
            const selectedComplement = shuffledExtraData.slice(0, neededComplement);
            
            // Combinar los resultados
            const combinedResults = [...data, ...selectedComplement];
            
            logger.debug(`Total de recomendaciones combinadas: ${combinedResults.length} (${data.length} principales + ${selectedComplement.length} complementarias)`);
            setRecomendados(combinedResults);
          } else {
            // Si el tipo complementario está excluido, usar solo lo que tenemos
            logger.debug(`No se pueden complementar con ${complementaryType}s porque están excluidas`);
            setRecomendados(data);
          }
        } else {
          // Si tenemos suficientes recomendaciones, usarlas tal como vienen
          logger.debug(`Se obtuvieron suficientes recomendaciones (${data.length})`);
          setRecomendados(data);
        }
      } catch (error) {
        logger.error('Error al cargar recomendados:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRecomendados();
  }, [currentModelId, modelType, limit, excludeTypes]);



  // Si no hay recomendados, no mostrar la sección
  if (!loading && recomendados.length === 0) {
    return null;
  }

  // Función para obtener la URL de la imagen según el tipo de modelo
  const getImageUrl = (modelo) => {
    if (!modelo) return '/img/placeholder.png';

    const service = modelo.modelType === 'independiente'
      ? independientesService
      : modelosService;

    const imageField = 'imagen_perfil';
    return service.getImageUrl(modelo, modelo[imageField]);
  };

  // Función para obtener la ruta de detalle según el tipo de modelo
  const getDetailPath = (modelo) => {
    if (!modelo) return '/';
    return modelo.modelType === 'independiente'
      ? `/independiente/${modelo.id}`
      : `/brujita/${modelo.id}`;
  };

  // Función para determinar si un modelo es VIP o destacado
  const isVIP = (modelo) => {
    if (!modelo) return false;
    return modelo.vip === true ||
           (Array.isArray(modelo.estado) && modelo.estado.includes('VIP')) ||
           modelo.estado === 'VIP';
  };

  const isDestacada = (modelo) => {
    if (!modelo) return false;
    return modelo.destacada === true ||
           (Array.isArray(modelo.estado) && modelo.estado.includes('Destacada')) ||
           modelo.estado === 'Destacada';
  };

  return (
    <div className="mt-12 mb-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">
          Recomendadas para ti
        </h2>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="w-12 h-12 relative animate-pulse-glow">
            <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#9c27b0] animate-spin"></div>
            <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#e91e63] animate-spin animate-reverse"></div>
          </div>
        </div>
      ) : (
        <div className="relative">
          {/* Indicador de desplazamiento horizontal en móviles */}
          <div className="md:hidden flex justify-center mb-4">
            <div className="flex items-center space-x-1">
              <span className="text-xs text-gray-400">Desliza</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
          
          {/* Carrusel en móviles, grid en desktop */}
          <div className={`
            md:grid md:gap-6 ${recomendados.length === 1 ? 'md:grid-cols-1 md:max-w-sm md:mx-auto' : 
            recomendados.length === 2 ? 'md:grid-cols-2 md:max-w-2xl md:mx-auto' : 
            'md:grid-cols-3'}
            
            /* Estilos para carrusel en móviles */
            flex overflow-x-auto pb-4 gap-4 snap-x snap-mandatory md:overflow-visible md:flex-wrap
            scrollbar-thin scrollbar-thumb-[#9c27b0] scrollbar-track-transparent
            scroll-smooth
          `}>
          {/* Los estilos para el scroll suave se aplican directamente en las clases de Tailwind */}
          {recomendados.map((modelo) => {
            // Determinar si es brujita o independiente para los estilos
            const isBrujita = modelo.modelType === 'brujita';
            const modelId = `${modelo.modelType}-${modelo.id}`;
            const isHovered = hoveredId === modelId;

            return (
              <Link
                key={modelId}
                to={getDetailPath(modelo)}
                className="block snap-center md:snap-align-none flex-shrink-0 w-[85vw] sm:w-[45vw] md:w-auto"
                onMouseEnter={() => setHoveredId(modelId)}
                onMouseLeave={() => setHoveredId(null)}
              >
                <div className="group relative rounded-xl overflow-hidden shadow-lg border border-[#3d3d5a] h-full">
                  {/* Overlay de gradiente */}
                  <div className="absolute inset-0 bg-gradient-to-t from-[#000000] via-transparent to-transparent opacity-70 z-10"></div>

                  {/* Imagen con LazyImage */}
                  <div className="h-80 overflow-hidden bg-[#1a1a1a]">
                    <LazyImage
                      src={getImageUrl(modelo)}
                      alt={modelo.nombre || 'Modelo'}
                      className={`w-full h-full transition-transform duration-700 ${isHovered ? 'scale-110' : 'scale-100'}`}
                      objectFit="cover"
                      loading="lazy"
                      onError={(e) => {
                        e.target.src = '/img/placeholder.png';
                      }}
                    />
                  </div>

                  {/* Información */}
                  <div className="absolute bottom-0 left-0 right-0 p-4 z-20">
                    <h3 className="text-xl font-bold text-white mb-1">{modelo.nombre || 'Sin nombre'}</h3>

                    {/* Si tiene ambos tags (VIP y destacada), mostrarlos en líneas separadas */}
                    {isVIP(modelo) && isDestacada(modelo) ? (
                      <>
                        {/* Primera línea: edad y perfil */}
                        <div className="flex flex-wrap items-center gap-2 mb-1">
                          {modelo.edad && (
                            <span className="bg-black bg-opacity-50 px-2 py-1 rounded-full text-xs text-white">
                              {modelo.edad} años
                            </span>
                          )}
                          {modelo.perfil_corporal && (
                            <span className="bg-[#2d2d2d] px-2 py-1 rounded-full text-xs text-gray-200">
                              {modelo.perfil_corporal}
                            </span>
                          )}
                        </div>
                        {/* Segunda línea: VIP y destacada */}
                        <div className="flex flex-wrap items-center gap-2">
                          <span className="badge-vip">
                            <span>⭐</span> VIP
                          </span>
                          <span className="badge-destacado">
                            <span>🔥</span> Destacada
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                            isBrujita
                              ? 'bg-[#9c27b0]/80 text-white'
                              : 'bg-[#3f51b5]/80 text-white'
                          }`}>
                            {isBrujita ? 'Brujita' : 'Independiente'}
                          </span>
                        </div>
                      </>
                    ) : (
                      /* Si solo tiene un tag o ninguno, mostrar todo en una línea pero con orden: primero edad y perfil, luego tags */
                      <div className="flex flex-wrap items-center gap-2">
                        {/* Primero edad y perfil */}
                        {modelo.edad && (
                          <span className="bg-black bg-opacity-50 px-2 py-1 rounded-full text-xs text-white">
                            {modelo.edad} años
                          </span>
                        )}
                        {modelo.perfil_corporal && (
                          <span className="bg-[#2d2d2d] px-2 py-1 rounded-full text-xs text-gray-200">
                            {modelo.perfil_corporal}
                          </span>
                        )}
                        {/* Después los tags de VIP o destacada */}
                        {isVIP(modelo) && (
                          <span className="badge-vip">
                            <span>⭐</span> VIP
                          </span>
                        )}
                        {isDestacada(modelo) && (
                          <span className="badge-destacado">
                            <span>🔥</span> Destacada
                          </span>
                        )}
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          isBrujita
                            ? 'bg-[#9c27b0]/80 text-white'
                            : 'bg-[#3f51b5]/80 text-white'
                        }`}>
                          {isBrujita ? 'Brujita' : 'Independiente'}
                        </span>
                      </div>
                    )}

                    {/* Zonas de trabajo para independientes */}
                    {!isBrujita && modelo.zonas && Array.isArray(modelo.zonas) && modelo.zonas.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {modelo.zonas.slice(0, 2).map((zona, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-[#3f51b5] bg-opacity-50 text-white"
                          >
                            <span className="mr-1">📍</span>
                            {zona}
                          </span>
                        ))}
                        {modelo.zonas.length > 2 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-[#3f51b5] bg-opacity-50 text-white">
                            +{modelo.zonas.length - 2}
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Efecto de brillo en hover */}
                  <div className={`absolute inset-0 transition-opacity duration-300 pointer-events-none ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
                    <div className={`absolute inset-0 bg-gradient-to-t ${
                      isBrujita
                        ? 'from-[#9c27b0]'
                        : 'from-[#3f51b5]'
                    } to-transparent opacity-30`}></div>
                    <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${
                      isBrujita
                        ? 'from-[#9c27b0] to-[#e91e63]'
                        : 'from-[#3f51b5] to-[#00bcd4]'
                    }`}></div>
                    <div className={`absolute inset-0 rounded-xl border-2 ${
                      isBrujita
                        ? 'border-[#9c27b0]'
                        : 'border-[#3f51b5]'
                    } opacity-50`}></div>
                    <div className={`absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t ${
                      isBrujita
                        ? 'from-[#9c27b0]'
                        : 'from-[#3f51b5]'
                    } to-transparent opacity-20`}></div>
                  </div>
                </div>
              </Link>
            );
          })}
          </div>
        </div>
      )}
    </div>
  );
};

export default RecomendadosSection;
