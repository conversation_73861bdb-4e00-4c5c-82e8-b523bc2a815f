import { Link } from 'react-router-dom';
import { modelosService } from '../services/pocketbase';
import logger from '../services/logService';

const ModeloCard = ({ modelo }) => {
  // Obtener los campos del modelo con fallbacks para diferentes nombres de campo
  const nombre = modelo.nombre || modelo.name || 'Sin nombre';
  const edad = modelo.edad || modelo.age || '';
  const perfilCorporal = modelo.perfil_corporal || modelo.profile || '';
  const estado = modelo.estado || modelo.status || '';

  // Manejar el campo medidas que puede ser un objeto
  const medidas = modelo.medidas || {};

  // Buscar la imagen destacada en diferentes posibles campos
  const imagenDestacada =
    modelo.imagen_destacada ||
    modelo.featured_image ||
    modelo.image ||
    modelo.foto ||
    modelo.photo ||
    '';

  // Obtener la URL de la imagen destacada
  const imageUrl = imagenDestacada
    ? modelosService.getImageUrl(modelo, imagenDestacada)
    : '/img/placeholder.png'; // Imagen de placeholder

  // Registrar los datos del modelo solo en modo debug
  logger.debug('Modelo en ModeloCard:', modelo);
  logger.debug('URL de imagen:', imageUrl);

  return (
    <Link to={`/brujita/${modelo.id}`} className="block">
      <div className="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:scale-105 hover:shadow-lg">
        <div className="h-64 overflow-hidden">
          <img
            src={imageUrl}
            alt={`Modelo ${nombre}`}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="p-4">
          <h3 className="text-xl font-semibold text-gray-800">{nombre}</h3>
          <div className="mt-2 flex justify-between text-gray-600">
            {edad && <span>{edad} años</span>}
            {perfilCorporal && <span>{perfilCorporal}</span>}
          </div>
          {estado && (
            <div className="mt-2">
              <span className={`px-2 py-1 text-xs rounded-full ${
                estado === 'Destacado'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-purple-100 text-purple-800'
              }`}>
                {estado}
              </span>
            </div>
          )}
        </div>
      </div>
    </Link>
  );
};

export default ModeloCard;
