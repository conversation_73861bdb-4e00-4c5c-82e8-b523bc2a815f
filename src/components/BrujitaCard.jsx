import { Link } from 'react-router-dom';
import { modelosService } from '../services/pocketbase';
import { useState, useEffect } from 'react';
import logger from '../services/logService';
import LazyImage from './LazyImage';

const BrujitaCard = ({ modelo }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Obtener los campos del modelo con fallbacks para diferentes nombres de campo
  const nombre = modelo.nombre || modelo.name || 'Sin nombre';
  const edad = modelo.edad || modelo.age || '';
  const perfilCorporal = modelo.perfil_corporal || modelo.profile || '';
  const estado = modelo.estado || modelo.status || '';

  // Verificar si la brujita es VIP o Destacada
  let isVIP = false;
  let isDestacada = false;

  // Verificar directamente en el objeto modelo completo
  if (modelo.estado) {
    if (typeof modelo.estado === 'string') {
      isVIP = modelo.estado === 'VIP' || modelo.estado.includes('VIP');
      isDestacada = modelo.estado === 'Destacada' || modelo.estado.includes('Destacada');
    } else if (Array.isArray(modelo.estado)) {
      // Verificar cada elemento del array
      modelo.estado.forEach(item => {
        if (typeof item === 'string') {
          if (item === 'VIP' || item.includes('VIP')) isVIP = true;
          if (item === 'Destacada' || item.includes('Destacada')) isDestacada = true;
        }
      });
    }
  }

  // Forzar los valores para pruebas (TEMPORAL)
  // Descomentar estas líneas si necesitas forzar los valores para pruebas
  // isVIP = true;
  // isDestacada = true;

  // Solo registrar en modo debug (no visible en producción)
  logger.debug(`Brujita ${nombre} (${modelo.id}):`, {
    estado: modelo.estado,
    isVIP,
    isDestacada
  });

  // Verificar otros campos posibles
  isVIP = isVIP ||
          modelo.vip === true ||
          modelo.isVIP === true ||
          modelo.estado_vip === true ||
          false;

  isDestacada = isDestacada ||
                modelo.destacada === true ||
                modelo.isDestacada === true ||
                modelo.estado_destacada === true ||
                false;

  // Manejar el campo medidas que puede ser un objeto
  const medidas = modelo.medidas || {};

  // Buscar la imagen de perfil en diferentes posibles campos
  const imagenPerfil =
    modelo.imagen_perfil ||
    modelo.profile_image ||
    modelo.photo ||
    modelo.foto ||
    modelo.imagen_destacada || // Fallback a imagen destacada si no hay imagen de perfil
    '';

  // Obtener la URL de la imagen de perfil
  const imageUrl = imagenPerfil
    ? modelosService.getImageUrl(modelo, imagenPerfil)
    : '/img/placeholder.png'; // Imagen de placeholder

  // Efecto para animar la entrada de la tarjeta
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, Math.random() * 300); // Efecto escalonado

    return () => clearTimeout(timer);
  }, []);

  return (
    <Link
      to={`/brujita/${modelo.id}`}
      className={`block transform transition-all duration-500 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="group relative rounded-xl overflow-hidden shadow-lg border border-[#3d3d5a]">
        {/* Overlay de gradiente */}
        <div className="absolute inset-0 bg-gradient-to-t from-[#000000] via-transparent to-transparent opacity-70 z-10"></div>

        {/* Imagen con LazyImage */}
        <div className="h-80 overflow-hidden">
          <LazyImage
            src={imageUrl}
            alt={`Brujita ${nombre}`}
            className={`w-full h-full transition-transform duration-700 ${isHovered ? 'scale-110' : 'scale-100'}`}
            objectFit="cover"
            onError={(e) => {
              e.target.src = '/img/placeholder.png';
            }}
          />
        </div>

        {/* Información */}
        <div className="absolute bottom-0 left-0 right-0 p-4 z-20">
          <h3 className="text-xl font-bold text-white mb-1">{nombre}</h3>
          {/* Si tiene ambos tags (VIP y destacada), mostrarlos en líneas separadas */}
          {isVIP && isDestacada ? (
            <>
              {/* Primera línea: edad y perfil */}
              <div className="flex flex-wrap items-center gap-2 mb-1">
                {edad && (
                  <span className="bg-black bg-opacity-50 px-2 py-1 rounded-full text-xs text-white">
                    {edad} años
                  </span>
                )}
                {perfilCorporal && (
                  <span className="bg-[#2d2d2d] px-2 py-1 rounded-full text-xs text-gray-200">
                    {perfilCorporal}
                  </span>
                )}
              </div>
              {/* Segunda línea: VIP y destacada */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="badge-vip">
                  <span>⭐</span> VIP
                </span>
                <span className="badge-destacado">
                  <span>🔥</span> Destacada
                </span>
              </div>
            </>
          ) : (
            /* Si solo tiene un tag o ninguno, mostrar todo en una línea pero con orden: primero edad y perfil, luego tags */
            <div className="flex flex-wrap items-center gap-2">
              {/* Primero edad y perfil */}
              {edad && (
                <span className="bg-black bg-opacity-50 px-2 py-1 rounded-full text-xs text-white">
                  {edad} años
                </span>
              )}
              {perfilCorporal && (
                <span className="bg-[#2d2d2d] px-2 py-1 rounded-full text-xs text-gray-200">
                  {perfilCorporal}
                </span>
              )}
              {/* Después los tags de VIP o destacada */}
              {isVIP && (
                <span className="badge-vip">
                  <span>⭐</span> VIP
                </span>
              )}
              {isDestacada && (
                <span className="badge-destacado">
                  <span>🔥</span> Destacada
                </span>
              )}
            </div>
          )}
        </div>

        {/* Efecto de brillo en hover */}
        <div className={`absolute inset-0 transition-opacity duration-300 pointer-events-none ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
          <div className="absolute inset-0 bg-gradient-to-t from-[#9c27b0] to-transparent opacity-30"></div>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#9c27b0] to-[#e91e63]"></div>
          <div className="absolute inset-0 rounded-xl border-2 border-[#9c27b0] opacity-50"></div>
          <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-[#9c27b0] to-transparent opacity-20"></div>
        </div>
      </div>
    </Link>
  );
};

export default BrujitaCard;
