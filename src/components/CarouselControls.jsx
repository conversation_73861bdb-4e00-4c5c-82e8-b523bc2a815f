import React from 'react';

const CarouselControls = ({ slidesCount, currentSlide, goToSlide }) => {
  return (
    <>
      {/* Indicadores del carrusel */}
      <div className="absolute bottom-3 left-0 right-0 z-30 flex justify-center space-x-2">
        {Array.from({ length: slidesCount }).map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-2 h-2 md:w-2.5 md:h-2.5 rounded-full transition-all duration-300 ${index === currentSlide ? 'bg-white scale-125' : 'bg-white/50 hover:bg-white/80'}`}
            aria-label={`Ir al slide ${index + 1}`}
          ></button>
        ))}
      </div>

      {/* Botones de navegación */}
      <button
        className="absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 z-30 bg-black/30 hover:bg-black/50 text-white rounded-full p-1.5 focus:outline-none transition-all duration-300"
        onClick={() => goToSlide((currentSlide - 1 + slidesCount) % slidesCount)}
        aria-label="Slide anterior"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button
        className="absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 z-30 bg-black/30 hover:bg-black/50 text-white rounded-full p-1.5 focus:outline-none transition-all duration-300"
        onClick={() => goToSlide((currentSlide + 1) % slidesCount)}
        aria-label="Siguiente slide"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </>
  );
};

export default CarouselControls;
