import { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';

/**
 * Componente de imagen con carga perezosa y efectos de transición
 * Utiliza IntersectionObserver para cargar imágenes solo cuando están cerca del viewport
 *
 * @param {string} src - URL de la imagen
 * @param {string} alt - Texto alternativo para la imagen
 * @param {string} className - Clases CSS adicionales
 * @param {string} placeholderSrc - URL de la imagen de placeholder (opcional)
 * @param {string} objectFit - Modo de ajuste de la imagen (cover, contain, fill, none, scale-down)
 * @param {function} onLoad - Función a ejecutar cuando la imagen se carga (opcional)
 * @param {function} onError - Función a ejecutar cuando hay un error al cargar la imagen (opcional)
 * @param {object} imgProps - Propiedades adicionales para pasar al elemento img
 */
const LazyImage = ({
  src,
  alt,
  className = '',
  placeholderSrc = '/img/placeholder.png',
  objectFit = 'cover',
  onLoad,
  onError,
  ...imgProps
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [imgSrc, setImgSrc] = useState(placeholderSrc);
  const imgRef = useRef(null);

  // Configurar IntersectionObserver para detectar cuando la imagen está cerca del viewport
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { rootMargin: '200px' } // Cargar imágenes cuando están a 200px de entrar en el viewport
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (imgRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  // Cargar la imagen real cuando está en el viewport
  useEffect(() => {
    if (!isInView) return;

    const img = new Image();
    img.src = src;
    img.onload = () => {
      setImgSrc(src);
      setIsLoaded(true);
      if (onLoad) onLoad();
    };
    img.onerror = () => {
      if (onError) onError();
      setImgSrc(placeholderSrc);
    };
  }, [isInView, src, placeholderSrc, onLoad, onError]);

  return (
    <div className={`relative ${className}`} ref={imgRef}>
      <img
        src={imgSrc}
        alt={alt}
        className={`w-full h-full transition-opacity duration-500 ${
          isLoaded ? 'opacity-100' : 'opacity-70'
        }`}
        style={{ objectFit }}
        {...imgProps}
      />
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-[#181828] bg-opacity-30">
          <div className="w-8 h-8 border-2 border-[#9c27b0] border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
};

LazyImage.propTypes = {
  src: PropTypes.string.isRequired,
  alt: PropTypes.string.isRequired,
  className: PropTypes.string,
  placeholderSrc: PropTypes.string,
  objectFit: PropTypes.oneOf(['cover', 'contain', 'fill', 'none', 'scale-down']),
  onLoad: PropTypes.func,
  onError: PropTypes.func,
};

export default LazyImage;
