import { Component } from 'react';
import { Link } from 'react-router-dom';
import logger from '../services/logService';

/**
 * Componente para capturar errores en la aplicación
 * Evita que la aplicación se rompa completamente cuando ocurre un error
 */
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Actualizar el estado para mostrar la UI de fallback
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Registrar el error para análisis
    logger.error('Error capturado por ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });

    // Aquí se podría enviar el error a un servicio de monitoreo
    // como Sentry, LogRocket, etc.
  }

  render() {
    if (this.state.hasError) {
      // Renderizar UI de fallback
      return (
        <div className="min-h-screen bg-[#121220] text-white flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-[#181828] p-8 rounded-lg shadow-lg border border-[#29293d]">
            <h1 className="text-2xl font-bold text-[#e0b3ff] mb-4 flex items-center gap-2">
              <span className="text-red-400">⚠️</span> Algo salió mal
            </h1>

            <p className="text-gray-300 mb-6">
              Lo sentimos, ha ocurrido un error inesperado. Nuestro equipo ha sido notificado.
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="bg-[#23233a] p-4 rounded-md border border-[#29293d] mb-6 overflow-auto max-h-40">
                <p className="text-red-400 font-mono text-sm">
                  {this.state.error.toString()}
                </p>
              </div>
            )}

            <div className="flex flex-col gap-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-md hover:opacity-90 transition-all flex items-center justify-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Recargar página
              </button>

              <Link
                to="/"
                className="w-full px-4 py-2 border border-[#29293d] text-gray-300 rounded-md hover:bg-[#29293d] transition-all flex items-center justify-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7m-7-7v14" />
                </svg>
                Volver al inicio
              </Link>
            </div>
          </div>
        </div>
      );
    }

    // Si no hay error, renderizar los hijos normalmente
    return this.props.children;
  }
}

export default ErrorBoundary;
