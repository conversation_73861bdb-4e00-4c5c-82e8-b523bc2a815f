import { useState, useEffect, useRef } from 'react';
import CarouselSlide from './CarouselSlide';
import CarouselControls from './CarouselControls';
import { bannerService } from '../services/bannerService';
import logger from '../services/logService';

const BannerCarousel = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const intervalRef = useRef(null);
  const [banners, setBanners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Cargar banners desde la base de datos
  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setLoading(true);
        const response = await bannerService.getAll();

        // Filtrar solo los banners activos y ordenarlos por el campo orden
        const activeBanners = response.items
          .filter(banner => banner.activo)
          .sort((a, b) => a.orden - b.orden);

        setBanners(activeBanners);
        logger.debug('Banners cargados:', activeBanners);
      } catch (err) {
        logger.error('Error al cargar banners:', err);
        setError('No se pudieron cargar los banners');
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, []);

  // Iniciar el carrusel automático
  useEffect(() => {
    if (banners.length > 0) {
      startCarouselInterval();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [banners]);

  // Función para iniciar el intervalo del carrusel
  const startCarouselInterval = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (banners.length > 0) {
      intervalRef.current = setInterval(() => {
        setCurrentSlide((prevSlide) => (prevSlide + 1) % banners.length);
      }, 5000); // Cambiar cada 5 segundos
    }
  };

  // Función para cambiar manualmente el slide
  const goToSlide = (index) => {
    setCurrentSlide(index);
    startCarouselInterval(); // Reiniciar el intervalo
  };

  // Convertir los banners al formato esperado por el carrusel
  const mapBannerToSlide = (banner) => {
    return {
      bgImage: banner.imagen ? bannerService.getImageUrl(banner, banner.imagen) : '/img/placeholder.png',
      title: banner.titulo || '',
      subtitle: banner.texto_resaltado || '',
      description: banner.descripcion || '',
      buttonText: banner.whatsapp_texto || 'Contactar',
      buttonLink: banner.whatsapp_enlace || '#',
      telegramText: banner.telegram_texto || '',
      telegramLink: banner.telegram_enlace || '',
      customText: banner.custom_texto || '',
      customLink: banner.custom_enlace || ''
    };
  };

  // Si no hay banners activos, mostrar un mensaje o un banner predeterminado
  if (loading) {
    return (
      <section className="relative mb-12 overflow-hidden rounded-xl h-[350px] md:h-[400px] shadow-lg bg-[#181828] flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto relative animate-pulse-glow">
            <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-[#9c27b0] animate-spin"></div>
            <div className="absolute inset-2 rounded-full border-r-2 border-l-2 border-[#e91e63] animate-spin animate-reverse"></div>
          </div>
          <p className="mt-4 text-gray-400">Cargando banners...</p>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="relative mb-12 overflow-hidden rounded-xl h-[350px] md:h-[400px] shadow-lg bg-[#181828] flex items-center justify-center">
        <div className="text-center text-gray-400">
          <p>No se pudieron cargar los banners</p>
        </div>
      </section>
    );
  }

  if (banners.length === 0) {
    return (
      <section className="relative mb-12 overflow-hidden rounded-xl h-[350px] md:h-[400px] shadow-lg bg-[#181828] flex items-center justify-center">
        <div className="text-center text-gray-400">
          <p>No hay banners disponibles</p>
        </div>
      </section>
    );
  }

  return (
    <section className="relative mb-12 overflow-hidden rounded-xl h-[350px] md:h-[400px] shadow-lg">
      {/* Slides del carrusel */}
      {banners.map((banner, index) => (
        <CarouselSlide
          key={banner.id}
          slide={mapBannerToSlide(banner)}
          isActive={index === currentSlide}
        />
      ))}

      {/* Controles del carrusel */}
      {banners.length > 1 && (
        <CarouselControls
          slidesCount={banners.length}
          currentSlide={currentSlide}
          goToSlide={goToSlide}
        />
      )}
    </section>
  );
};

export default BannerCarousel;
