import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { authService } from '../services/pocketbase';

const AdminLayout = ({ children }) => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  // Detectar scroll para cambiar el estilo del header
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  const handleLogout = () => {
    // Cerrar la sesión
    authService.logout();
    
    // Redirigir a la página principal
    navigate('/');
  };

  return (
    <div className="min-h-screen flex flex-col bg-[#121212] text-white">
      {/* Header */}
      <header
        className={`fixed w-full z-50 transition-all duration-300 ${
          scrolled
            ? 'bg-[#121212] bg-opacity-90 backdrop-blur-md shadow-lg'
            : 'bg-transparent'
        }`}
      >
        <div className="container-custom">
          <div className="flex justify-between h-16 md:h-20 items-center">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link
                to="/admin"
                className="flex items-center"
              >
                <img src="/img/Logo_h.svg" alt="Brujitas Logo" className="h-10 md:h-12 w-auto logo-normal" />
              </Link>
            </div>

            {/* Navigation - Desktop */}
            <div className="hidden md:flex items-center space-x-6">
              {/* Nombre del Admin y Botón de Logout */}
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-[#e0b3ff]">Admin</span>
                <button
                  onClick={handleLogout}
                  className="px-4 py-2 text-sm font-medium text-white bg-[#e91e63] hover:bg-[#d81557] rounded-md transition-colors"
                >
                  Cerrar Sesión
                </button>
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2 rounded-md text-gray-300 hover:text-white focus:outline-none"
              >
                {mobileMenuOpen ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-[#121212] bg-opacity-95 backdrop-blur-md border-b border-[#3d3d3d] py-3">
            <div className="container-custom space-y-4">
              <div className="px-4 py-2 text-sm font-medium text-[#e0b3ff]">Admin</div>
              <button
                onClick={handleLogout}
                className="w-full text-left px-4 py-2 text-sm font-medium text-white hover:bg-[#e91e63] transition-colors"
              >
                Cerrar Sesión
              </button>
            </div>
          </div>
        )}
      </header>

      {/* Main content */}
      <main className="flex-grow pt-16 md:pt-20">
        <div className="container-custom py-6 md:py-10">
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-[#1e1e1e] border-t border-[#3d3d3d] py-4">
        <div className="container-custom text-center text-sm text-gray-400">
          Panel de Administración - Brujitas
        </div>
      </footer>
    </div>
  );
};

export default AdminLayout;