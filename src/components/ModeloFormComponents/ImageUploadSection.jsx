import React from 'react';

const ImageUploadSection = ({ imagePreviews, errors, handleChange, handleRemoveImage, isEditing }) => {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
        <span className="text-[#9c27b0]">🖼️</span> Imágenes
      </h3>
      <div className="bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner space-y-6">
        {/* Imagen de Perfil */}
        <div className="space-y-2">
          <label className="block text-sm font-semibold text-gray-200 flex items-center gap-1">
            <span className="text-[#9c27b0]">👤</span> Imagen de Perfil {isEditing ? '(opcional)' : <span className="text-pink-400">*</span>}
          </label>
          <div className="relative border-2 border-dashed border-[#29293d] hover:border-[#9c27b0] transition-colors rounded-lg p-4 text-center cursor-pointer bg-[#181828]">
            <input
              type="file"
              name="imagen_perfil"
              accept="image/*"
              onChange={handleChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
            />
            {!imagePreviews.imagen_perfil ? (
              <div className="flex flex-col items-center justify-center py-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#9c27b0] mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <p className="text-sm text-gray-400">Haz clic para seleccionar o arrastra una imagen</p>
              </div>
            ) : (
              <div className="relative">
                <img src={imagePreviews.imagen_perfil} alt="Vista previa perfil" className="w-full h-32 object-cover rounded-lg" />
                <button
                  type="button"
                  onClick={() => handleRemoveImage('imagen_perfil')}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 shadow-lg hover:bg-red-600 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs py-1 px-2">
                  Imagen cargada
                </div>
              </div>
            )}
          </div>
          {errors.imagen_perfil && <p className="text-red-400 text-xs mt-1">{errors.imagen_perfil}</p>}
        </div>

        {/* Galería */}
        <div className="space-y-2">
          <label className="block text-sm font-semibold text-gray-200 flex items-center gap-1">
            <span className="text-[#ff9800]">📸</span> Galería de imágenes
          </label>
          <div className="relative border-2 border-dashed border-[#29293d] hover:border-[#ff9800] transition-colors rounded-lg p-4 text-center cursor-pointer bg-[#181828]">
            <input
              type="file"
              name="galeria"
              accept="image/*"
              multiple
              onChange={handleChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
            />
            <div className="flex flex-col items-center justify-center py-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#ff9800] mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-sm text-gray-400">Haz clic para seleccionar o arrastra múltiples imágenes</p>
              <p className="text-xs text-gray-500 mt-1">Puedes seleccionar varias imágenes a la vez</p>
            </div>
          </div>

          {/* Mostrar imágenes cargadas */}
          {imagePreviews.galeria && imagePreviews.galeria.length > 0 && (
            <div className="mt-4">
              <p className="text-sm text-gray-400 mb-2">Imágenes en la galería:</p>
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-2">
                {imagePreviews.galeria.map((img, idx) => (
                  <div key={idx} className="relative group">
                    <img src={img.url} alt={`Galería ${idx + 1}`} className="w-full h-16 object-cover rounded-lg border border-[#29293d]" />
                    <button
                      type="button"
                      onClick={() => handleRemoveImage('galeria', idx)}
                      className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 shadow-lg hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                    <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs py-1 px-1 text-center opacity-0 group-hover:opacity-100">
                      {img.file ? 'Nueva' : 'Existente'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImageUploadSection;
