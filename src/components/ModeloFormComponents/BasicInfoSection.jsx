import React from 'react';

const BasicInfoSection = ({ formData, errors, handleChange, handleSwitchChange, toggleDisponibilidad }) => {
  return (
    <div className="flex flex-col gap-6 mb-8">
      {/* Nombre y Edad en dos columnas */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-semibold text-gray-200 mb-1">Nombre <span className="text-pink-400">*</span></label>
          <input
            type="text"
            name="nombre"
            value={formData.nombre}
            onChange={handleChange}
            className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400 ${errors.nombre ? 'border-red-400' : 'border-[#29293d]'}`}
            placeholder="Ej: Luna"
          />
          {errors.nombre && <p className="text-red-400 text-xs mt-1">{errors.nombre}</p>}
        </div>
        <div>
          <label className="block text-sm font-semibold text-gray-200 mb-1">Edad <span className="text-pink-400">*</span></label>
          <input
            type="number"
            name="edad"
            value={formData.edad}
            onChange={handleChange}
            className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400 ${errors.edad ? 'border-red-400' : 'border-[#29293d]'}`}
            placeholder="Ej: 22"
          />
          {errors.edad && <p className="text-red-400 text-xs mt-1">{errors.edad}</p>}
        </div>
      </div>

      {/* Perfil Corporal en ancho completo */}
      <div className="w-full">
        <label className="block text-sm font-semibold text-gray-200 mb-1">Perfil Corporal <span className="text-pink-400">*</span></label>
        <select
          name="perfil_corporal"
          value={formData.perfil_corporal}
          onChange={handleChange}
          className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white ${errors.perfil_corporal ? 'border-red-400' : 'border-[#29293d]'}`}
        >
          <option value="">Selecciona una opción</option>
          <option value="Petite">Petite</option>
          <option value="Atletica">Atletica</option>
          <option value="Skinny">Skinny</option>
          <option value="Curvy">Curvy</option>
          <option value="Chubby">Chubby</option>
          <option value="Plus Size">Plus Size</option>
        </select>
        {errors.perfil_corporal && <p className="text-red-400 text-xs mt-1">{errors.perfil_corporal}</p>}
      </div>

      {/* Destacada y VIP en dos columnas */}
      <div className="grid grid-cols-2 gap-4">
        <button
          type="button"
          onClick={() => handleSwitchChange({ target: { name: 'destacada', checked: !formData.destacada } })}
          className={`flex items-center justify-between p-3 rounded-md border ${formData.destacada ? 'bg-gradient-to-r from-[#ff9800] to-[#f44336] text-white border-[#ff9800]' : 'bg-[#29293d] text-gray-200 border-[#29293d] hover:border-[#ff9800]'} transition-all`}
        >
          <div className="flex items-center">
            <span className="mr-2">🔥</span>
            <span>Destacada</span>
          </div>
          <div className="flex items-center">
            {formData.destacada && <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-md">Activado</span>}
          </div>
        </button>

        <button
          type="button"
          onClick={() => handleSwitchChange({ target: { name: 'vip', checked: !formData.vip } })}
          className={`flex items-center justify-between p-3 rounded-md border ${formData.vip ? 'bg-gradient-to-r from-[#e91e63] to-[#9c27b0] text-white border-[#e91e63]' : 'bg-[#29293d] text-gray-200 border-[#29293d] hover:border-[#e91e63]'} transition-all`}
        >
          <div className="flex items-center">
            <span className="mr-2">⭐</span>
            <span>VIP</span>
          </div>
          <div className="flex items-center">
            {formData.vip && <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-md">Activado</span>}
          </div>
        </button>
      </div>

      {/* Disponible en ancho completo */}
      <button
        type="button"
        onClick={toggleDisponibilidad}
        className={`w-full flex items-center justify-between p-3 rounded-md border ${formData.disponibilidad === 'Disponible' ? 'bg-gradient-to-r from-[#4CAF50]/20 to-[#2E7D32]/20 text-white border-[#4CAF50]' : 'bg-gradient-to-r from-[#f44336]/20 to-[#d32f2f]/20 text-white border-[#f44336]'} transition-all`}
      >
        <div className="flex items-center">
          <span className="mr-2">{formData.disponibilidad === 'Disponible' ? '🟢' : '🔴'}</span>
          <span>{formData.disponibilidad}</span>
        </div>
        <div className="flex items-center">
          <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">
            {formData.disponibilidad === 'Disponible' ? 'Activa' : 'Inactiva'}
          </span>
        </div>
      </button>
    </div>
  );
};

export default BasicInfoSection;
