import React from 'react';

const AvailabilitySection = ({ formData, handleDayBadgeClick }) => {
  const days = ['lunes', 'martes', 'miercoles', 'jueves', 'viernes', 'sabado', 'domingo'];
  const dayLabels = ['Lun', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'];

  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
        <span className="text-[#9c27b0]">📅</span> Horarios Disponibles
      </h3>
      <div className="bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner">
        <p className="text-sm text-gray-300 mb-4">Selecciona los días en que la brujita está disponible:</p>
        <div className="grid grid-cols-7 gap-2">
          {days.map((day, index) => (
            <button
              key={day}
              type="button"
              onClick={() => handleDayBadgeClick(day)}
              className={`w-full py-2 rounded-md text-sm font-medium transition-all ${formData.dias_disponibles[day] ? 'bg-gradient-to-r from-[#4CAF50]/20 to-[#2E7D32]/20 text-white border border-[#4CAF50]' : 'bg-[#29293d] text-gray-400 hover:bg-[#333344]'}`}
            >
              {dayLabels[index]}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AvailabilitySection;
