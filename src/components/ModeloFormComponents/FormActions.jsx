import React from 'react';
import { useNavigate } from 'react-router-dom';

const FormActions = ({ isSubmitting, isEditing, errors }) => {
  const navigate = useNavigate();

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8">
        <button
          type="button"
          onClick={() => navigate('/admin')}
          className="px-6 py-3 rounded-lg border border-[#29293d] bg-[#23233a] text-gray-300 hover:bg-[#29293d] transition-all flex items-center justify-center gap-2 shadow-md w-full"
          disabled={isSubmitting}
        >
          Cancelar
        </button>
        <button
          type="submit"
          className="px-8 py-3 rounded-lg bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white font-bold shadow-lg hover:from-[#6a0dad] hover:to-[#ad1457] transition-all text-lg flex items-center justify-center gap-2 transform hover:scale-105 duration-200 w-full"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Guardando...
            </>
          ) : (
            <>
              {isEditing ? (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Actualizar Brujita
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Nueva Brujita
                </>
              )}
            </>
          )}
        </button>
      </div>
      {errors.submit && (
        <div className="bg-[#2d0a16] p-4 rounded-lg border border-red-800 mt-4 text-center">
          <p className="text-red-400">{errors.submit}</p>
        </div>
      )}
    </>
  );
};

export default FormActions;
