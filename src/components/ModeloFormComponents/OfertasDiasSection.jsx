import React, { useState, useEffect } from 'react';
import { ofertasService } from '../../services/ofertasService';
import logger from '../../services/logService';

const OfertasDiasSection = ({ formData, setFormData }) => {
  const [ofertas, setOfertas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const days = ['lunes', 'martes', 'miercoles', 'jueves', 'viernes', 'sabado', 'domingo'];
  const dayLabels = ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'];

  // Cargar ofertas activas
  useEffect(() => {
    const fetchOfertas = async () => {
      try {
        setLoading(true);
        // Obtener solo ofertas visibles/activas
        const response = await ofertasService.getAll({ visible: true });
        setOfertas(response.items || []);
        setError(null);
      } catch (err) {
        logger.error('Error al cargar ofertas:', err);
        setError('No se pudieron cargar las ofertas. Por favor, intenta de nuevo.');
      } finally {
        setLoading(false);
      }
    };

    fetchOfertas();
  }, []);

  // Manejar selección de días para una oferta
  const handleDayClick = (ofertaId, day) => {
    setFormData(prev => {
      // Crear una copia del objeto ofertas_dias actual
      const newOfertasDias = { ...prev.ofertas_dias };

      // Inicializar el array para esta oferta si no existe
      if (!newOfertasDias[ofertaId]) {
        newOfertasDias[ofertaId] = [];
      }

      // Verificar si el día ya está seleccionado
      const isSelected = newOfertasDias[ofertaId].includes(day);

      if (isSelected) {
        // Si ya está seleccionado, quitarlo
        newOfertasDias[ofertaId] = newOfertasDias[ofertaId].filter(d => d !== day);
      } else {
        // Si no está seleccionado, agregarlo
        newOfertasDias[ofertaId] = [...newOfertasDias[ofertaId], day];
      }

      // Si no quedan días seleccionados, eliminar la entrada para esta oferta
      if (newOfertasDias[ofertaId].length === 0) {
        delete newOfertasDias[ofertaId];
      }

      return {
        ...prev,
        ofertas_dias: newOfertasDias
      };
    });
  };

  // Si no hay ofertas activas, no mostrar esta sección
  if (!loading && ofertas.length === 0) {
    return null;
  }

  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
        <span className="text-[#9c27b0]">🎁</span> Dias de oferta
      </h3>
      <div className="bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner">
        {loading ? (
          <div className="flex justify-center items-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-pink-500"></div>
          </div>
        ) : error ? (
          <p className="text-red-400 text-center py-2">{error}</p>
        ) : (
          <div className="space-y-6">
            <p className="text-sm text-gray-300 mb-4">
              Selecciona el dia(s) en los que la brujita tendrá la oferta.
            </p>

            {ofertas.map(oferta => (
              <div key={oferta.id} className="border-b border-[#29293d] pb-4 last:border-0 last:pb-0">
                <h4 className="text-md font-medium text-white mb-2 flex items-center">
                  <span className="text-[#e91e63] mr-2">✨</span>
                  {oferta.titulo}
                </h4>
                {(() => {
                  try {
                    // Intentar parsear la descripción como JSON
                    const secciones = JSON.parse(oferta.descripcion);

                    // Verificar si es un array válido
                    if (Array.isArray(secciones) && secciones.length > 0) {
                      return (
                        <div className="text-sm text-gray-300 mb-3">
                          {secciones.length > 1 ? (
                            <p className="text-xs text-gray-400 mb-1">
                              {secciones.length} secciones
                            </p>
                          ) : (
                            <p className="text-xs text-gray-400 mb-1">
                              {secciones[0].titulo}
                            </p>
                          )}
                        </div>
                      );
                    }
                  } catch (error) {
                    // Si no se puede parsear como JSON, mostrar un mensaje
                    return (
                      <p className="text-sm text-gray-300 mb-3 line-clamp-2">
                        Oferta
                      </p>
                    );
                  }

                  // Si llegamos aquí, algo salió mal
                  return (
                    <p className="text-sm text-gray-300 mb-3 line-clamp-2">
                      Sin detalles disponibles
                    </p>
                  );
                })()}

                <div className="grid grid-cols-7 gap-2">
                  {days.map((day, index) => {
                    const isSelected = formData.ofertas_dias[oferta.id]?.includes(day);

                    return (
                      <button
                        key={`${oferta.id}-${day}`}
                        type="button"
                        onClick={() => handleDayClick(oferta.id, day)}
                        className={`w-full py-2 rounded-md text-sm font-medium transition-all ${
                          isSelected
                            ? 'bg-gradient-to-r from-[#e91e63]/20 to-[#9c27b0]/20 text-white border border-[#e91e63]'
                            : 'bg-[#29293d] text-gray-400 hover:bg-[#333344]'
                        }`}
                      >
                        {dayLabels[index]}
                      </button>
                    );
                  })}
                </div>

                <div className="mt-2 text-xs text-gray-400">
                  {formData.ofertas_dias[oferta.id]?.length
                    ? `Días seleccionados: ${formData.ofertas_dias[oferta.id].length}`
                    : "No hay días seleccionados para esta oferta"}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default OfertasDiasSection;
