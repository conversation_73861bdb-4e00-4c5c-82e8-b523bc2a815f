import React from 'react';

const PersonalInfoSection = ({ formData, handleChange }) => {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
        <span className="text-[#9c27b0]">📝</span> Información Personal
      </h3>
      <div className="bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner space-y-6">
        <div>
          <label className="block text-sm font-semibold text-gray-200 mb-2 flex items-center gap-1">
            <span className="text-[#e91e63]">✨</span> Biografía
          </label>
          <textarea
            name="biografia"
            value={formData.biografia}
            onChange={handleChange}
            className="w-full p-4 border rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#181828] text-white border-[#29293d] min-h-[120px] resize-y"
            placeholder="Cuéntanos sobre la brujita..."
          />
          <p className="text-xs text-gray-400 mt-1 italic">Describe los detalles más interesantes sobre esta brujita</p>
        </div>
      </div>
    </div>
  );
};

export default PersonalInfoSection;
