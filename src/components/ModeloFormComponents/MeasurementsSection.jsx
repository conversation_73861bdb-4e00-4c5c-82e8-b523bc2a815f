import React from 'react';

const MeasurementsSection = ({ formData, handleChange }) => {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
        <span className="text-[#9c27b0]">📏</span> Medidas
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner">
        <div className="relative">
          <label className="block text-sm font-semibold text-gray-200 mb-2 flex items-center gap-1">
            <span className="text-[#4CAF50]">📏</span> Altura
          </label>
          <input
            type="number"
            name="altura"
            value={formData.altura}
            onChange={handleChange}
            className="w-full p-3 pl-10 border rounded-lg focus:ring-2 focus:ring-[#4CAF50] bg-[#181828] text-white border-[#29293d]"
            placeholder="cm"
          />
          <div className="absolute bottom-3 left-3 text-[#4CAF50] opacity-70">cm</div>
        </div>
        <div className="relative">
          <label className="block text-sm font-semibold text-gray-200 mb-2 flex items-center gap-1">
            <span className="text-[#e91e63]">🍒</span> Busto
          </label>
          <input
            type="number"
            name="busto"
            value={formData.medidas.busto}
            onChange={handleChange}
            className="w-full p-3 pl-10 border rounded-lg focus:ring-2 focus:ring-[#e91e63] bg-[#181828] text-white border-[#29293d]"
            placeholder="cm"
          />
          <div className="absolute bottom-3 left-3 text-[#e91e63] opacity-70">cm</div>
        </div>
        <div className="relative">
          <label className="block text-sm font-semibold text-gray-200 mb-2 flex items-center gap-1">
            <span className="text-[#9c27b0]">👙</span> Cintura
          </label>
          <input
            type="number"
            name="cintura"
            value={formData.medidas.cintura}
            onChange={handleChange}
            className="w-full p-3 pl-10 border rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#181828] text-white border-[#29293d]"
            placeholder="cm"
          />
          <div className="absolute bottom-3 left-3 text-[#9c27b0] opacity-70">cm</div>
        </div>
        <div className="relative">
          <label className="block text-sm font-semibold text-gray-200 mb-2 flex items-center gap-1">
            <span className="text-[#ff9800]">🍑</span> Cadera
          </label>
          <input
            type="number"
            name="cadera"
            value={formData.medidas.cadera}
            onChange={handleChange}
            className="w-full p-3 pl-10 border rounded-lg focus:ring-2 focus:ring-[#ff9800] bg-[#181828] text-white border-[#29293d]"
            placeholder="cm"
          />
          <div className="absolute bottom-3 left-3 text-[#ff9800] opacity-70">cm</div>
        </div>
      </div>
    </div>
  );
};

export default MeasurementsSection;
