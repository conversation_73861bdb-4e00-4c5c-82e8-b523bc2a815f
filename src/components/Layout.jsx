import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useConfig } from '../context/ConfigContext';

const Layout = ({ children }) => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { wa, telegram, tiktok, twitter } = useConfig();

  // Detectar scroll para cambiar el estilo del header
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  // No se necesitan funciones adicionales

  return (
    <div className="min-h-screen flex flex-col bg-[#121212] text-white">
      {/* Header */}
      <header
        className={`fixed w-full z-50 transition-all duration-300 ${
          scrolled
            ? 'bg-[#121212] bg-opacity-90 backdrop-blur-md shadow-lg'
            : 'bg-transparent'
        }`}
      >
        <div className="container-custom">
          <div className="flex justify-between h-16 md:h-20 items-center">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link
                to="/"
                className="flex items-center"
              >
                <img src="/img/Logo_h.svg" alt="Brujitas Logo" className="h-10 md:h-12 w-auto logo-normal" />
              </Link>
            </div>

            {/* Navigation - Desktop */}
            <div className="hidden md:flex items-center space-x-6">
              <Link
                to="/"
                className="px-1 py-2 text-sm font-medium text-white hover:text-[#e91e63] transition-colors border-b-2 border-transparent hover:border-[#e91e63]"
              >
                Brujitas
              </Link>
              <Link
                to="/bienvenida"
                className="px-1 py-2 text-sm font-medium text-white hover:text-[#e91e63] transition-colors border-b-2 border-transparent hover:border-[#e91e63]"
              >
                Bienvenida
              </Link>
              <Link
                to="/ofertas"
                className="px-1 py-2 text-sm font-medium text-white hover:text-[#e91e63] transition-colors border-b-2 border-transparent hover:border-[#e91e63]"
              >
                Ofertas
              </Link>
              <Link
                to="/clientesvip"
                className="px-1 py-2 text-sm font-medium text-white hover:text-[#e91e63] transition-colors border-b-2 border-transparent hover:border-[#e91e63]"
              >
                Clientes VIP
              </Link>
              <Link
                to="/independientes"
                className="px-1 py-2 text-sm font-medium text-white hover:text-[#e91e63] transition-colors border-b-2 border-transparent hover:border-[#e91e63]"
              >
                Independientes
              </Link>
              <a
                href="https://t.me/ReferenciasBSMx"
                target="_blank"
                rel="noopener noreferrer"
                className="px-1 py-2 text-sm font-medium text-white hover:text-[#e91e63] transition-colors border-b-2 border-transparent hover:border-[#e91e63]"
              >
                Referencias
              </a>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2 rounded-md text-gray-300 hover:text-white focus:outline-none"
              >
                {mobileMenuOpen ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-[#121212] bg-opacity-95 backdrop-blur-md border-b border-[#3d3d3d] py-3">
            <div className="container-custom space-y-4">
              <Link
                to="/"
                className="block px-4 py-2 text-sm font-medium text-white border-l-2 border-[#e91e63] hover:bg-[#1e1e1e]"
                onClick={() => setMobileMenuOpen(false)}
              >
                Brujitas
              </Link>
              <Link
                to="/bienvenida"
                className="block px-4 py-2 text-sm font-medium text-white border-l-2 border-[#e91e63] hover:bg-[#1e1e1e]"
                onClick={() => setMobileMenuOpen(false)}
              >
                Bienvenida
              </Link>
              <Link
                to="/ofertas"
                className="block px-4 py-2 text-sm font-medium text-white border-l-2 border-[#e91e63] hover:bg-[#1e1e1e]"
                onClick={() => setMobileMenuOpen(false)}
              >
                Ofertas
              </Link>
              <Link
                to="/clientesvip"
                className="block px-4 py-2 text-sm font-medium text-white border-l-2 border-[#e91e63] hover:bg-[#1e1e1e]"
                onClick={() => setMobileMenuOpen(false)}
              >
                Clientes VIP
              </Link>
              <Link
                to="/independientes"
                className="block px-4 py-2 text-sm font-medium text-white border-l-2 border-[#e91e63] hover:bg-[#1e1e1e]"
                onClick={() => setMobileMenuOpen(false)}
              >
                Independientes
              </Link>
              <a
                href="https://t.me/ReferenciasBSMx"
                target="_blank"
                rel="noopener noreferrer"
                className="block px-4 py-2 text-sm font-medium text-white border-l-2 border-[#e91e63] hover:bg-[#1e1e1e]"
                onClick={() => setMobileMenuOpen(false)}
              >
                Referencias
              </a>
            </div>
          </div>
        )}
      </header>

      {/* Main content */}
      <main className="flex-grow pt-16 md:pt-20">
        <div className="container-custom py-6 md:py-10">
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-[#1e1e1e] border-t border-[#3d3d3d] py-8">
        <div className="container-custom">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0 flex flex-col items-center md:items-start">
              <Link
                to="/"
                className="flex items-center justify-center mb-2"
              >
                <img src="/img/Logo_h.svg" alt="Brujitas Logo" className="h-10 md:h-10 w-auto logo-normal" />

              </Link>

              {/* Redes sociales */}
              <div className="flex items-center mt-4 space-x-4 justify-center md:justify-start w-full">
                {wa && (
                  <a
                    href={wa}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#e0b3ff] hover:text-white hover:bg-gradient-to-r hover:from-[#9c27b0] hover:to-[#e91e63] p-2 rounded-full transition-all duration-300"
                    aria-label="WhatsApp"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
                    </svg>
                  </a>
                )}
                {telegram && (
                  <a
                    href={telegram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#e0b3ff] hover:text-white hover:bg-gradient-to-r hover:from-[#9c27b0] hover:to-[#e91e63] p-2 rounded-full transition-all duration-300"
                    aria-label="Telegram"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                    </svg>
                  </a>
                )}
                {twitter && (
                  <a
                    href={twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#e0b3ff] hover:text-white hover:bg-gradient-to-r hover:from-[#9c27b0] hover:to-[#e91e63] p-2 rounded-full transition-all duration-300"
                    aria-label="Twitter/X"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                  </a>
                )}
                {tiktok && (
                  <a
                    href={tiktok}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#e0b3ff] hover:text-white hover:bg-gradient-to-r hover:from-[#9c27b0] hover:to-[#e91e63] p-2 rounded-full transition-all duration-300"
                    aria-label="TikTok"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                    </svg>
                  </a>
                )}
              </div>
            </div>
            <div className="text-center md:text-right text-gray-400 text-sm mt-6 md:mt-0">
              {/* Se eliminó el botón de Bienvenida */}
              <p>&copy; {new Date().getFullYear()} Brujitas Sexys Mx. Todos los derechos reservados.</p>
              <p className="mt-2">Encuentra tu brujita ideal ✨</p>
              <p className="mt-3 text-xs text-gray-500">Síguenos en nuestras redes sociales</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
