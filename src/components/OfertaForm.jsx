import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ofertasService } from '../services/ofertasService';
import { modelosService } from '../services/pocketbase';
import logger from '../services/logService';
import SeccionesDescripcion from './OfertaFormComponents/SeccionesDescripcion';

const OfertaForm = ({ oferta = null, onSubmit }) => {
  const navigate = useNavigate();
  const isEditing = !!oferta;

  // Estado para modelos disponibles
  const [modelos, setModelos] = useState([]);
  const [loadingModelos, setLoadingModelos] = useState(true);

  // Estado inicial del formulario
  const [formData, setFormData] = useState({
    titulo: '',
    secciones: [{ titulo: '', descripcion: '' }],
    visible: true
  });

  // Estado para errores
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Cargar modelos disponibles
  useEffect(() => {
    const fetchModelos = async () => {
      try {
        setLoadingModelos(true);
        const response = await modelosService.getAll();
        setModelos(response.items);
      } catch (err) {
        logger.error('Error al cargar modelos:', err);
      } finally {
        setLoadingModelos(false);
      }
    };

    fetchModelos();
  }, []);

  // Cargar datos de la oferta si estamos editando
  useEffect(() => {
    if (isEditing && oferta) {
      // Inicializar con valores por defecto
      let secciones = [{ titulo: '', descripcion: '' }];

      try {
        if (oferta.descripcion) {
          // Si la descripción es un string, intentar parsearlo como JSON
          if (typeof oferta.descripcion === 'string') {
            try {
              const parsedSecciones = JSON.parse(oferta.descripcion);
              if (Array.isArray(parsedSecciones) && parsedSecciones.length > 0) {
                // Asegurarse de que cada sección tenga la estructura correcta
                secciones = parsedSecciones.map(seccion => ({
                  titulo: seccion.titulo || '',
                  descripcion: seccion.descripcion || ''
                }));

                // Registrar para depuración
                logger.info('Secciones parseadas correctamente:', secciones);
              }
            } catch (parseError) {
              // Si no se puede parsear como JSON, crear una sección con la descripción completa
              secciones = [{
                titulo: 'Descripción',
                descripcion: oferta.descripcion || ''
              }];
              logger.warn('No se pudo parsear la descripción como JSON, usando formato antiguo', parseError);
            }
          }
          // Si la descripción ya es un objeto (array), usarlo directamente
          else if (Array.isArray(oferta.descripcion)) {
            secciones = oferta.descripcion.map(seccion => ({
              titulo: seccion.titulo || '',
              descripcion: seccion.descripcion || ''
            }));
            logger.info('Usando secciones del objeto directamente:', secciones);
          }
          // Si es otro tipo de objeto, intentar convertirlo
          else if (typeof oferta.descripcion === 'object') {
            try {
              // Intentar convertir el objeto a JSON y luego parsearlo
              const jsonStr = JSON.stringify(oferta.descripcion);
              const parsedObj = JSON.parse(jsonStr);

              if (Array.isArray(parsedObj) && parsedObj.length > 0) {
                secciones = parsedObj.map(seccion => ({
                  titulo: seccion.titulo || '',
                  descripcion: seccion.descripcion || ''
                }));
                logger.info('Convertido objeto a secciones:', secciones);
              } else {
                // Si no es un array, crear una sección con una representación del objeto
                secciones = [{
                  titulo: 'Descripción',
                  descripcion: jsonStr || ''
                }];
              }
            } catch (objError) {
              logger.warn('Error al convertir objeto a secciones:', objError);
              secciones = [{ titulo: 'Descripción', descripcion: 'Contenido no disponible' }];
            }
          }
        }
      } catch (error) {
        logger.error('Error general al procesar secciones:', error);
        secciones = [{ titulo: 'Descripción', descripcion: 'Error al cargar contenido' }];
      }

      // Actualizar el estado del formulario
      setFormData({
        titulo: oferta.titulo || '',
        secciones: secciones,
        visible: oferta.visible || false
      });
    }
  }, [oferta, isEditing]);

  // Manejar cambios en los campos del formulario
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      // Para checkboxes
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      // Para campos de texto
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Función para alternar el estado visible
  const toggleVisible = () => {
    setFormData(prev => ({
      ...prev,
      visible: !prev.visible
    }));
  };

  // Función para manejar cambios en las secciones
  const handleSeccionesChange = (secciones) => {
    setFormData(prev => ({
      ...prev,
      secciones
    }));
  };

  // Ya no necesitamos los métodos de manejo de modelos y días

  // Validar el formulario
  const validateForm = () => {
    const newErrors = {};

    if (!formData.titulo) newErrors.titulo = 'El título es obligatorio';

    // Validar que todas las secciones tengan título y descripción
    let seccionesValidas = true;
    const seccionesErrors = [];

    formData.secciones.forEach((seccion, index) => {
      if (!seccion.titulo || !seccion.descripcion) {
        seccionesValidas = false;
        seccionesErrors.push(`La sección ${index + 1} debe tener título y contenido`);
      }
    });

    if (!seccionesValidas) {
      newErrors.secciones = seccionesErrors;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manejar envío del formulario
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setIsSubmitting(true);

      // Crear FormData para enviar al servidor
      const formDataToSend = new FormData();

      // Agregar campos de texto
      formDataToSend.append('titulo', formData.titulo);

      // Convertir las secciones a JSON y guardarlas en el campo descripcion
      const descripcionJSON = JSON.stringify(formData.secciones);
      formDataToSend.append('descripcion', descripcionJSON);

      formDataToSend.append('visible', formData.visible);

      // Ya no agregamos modelos específicos porque la oferta aplica a todas las brujitas

      // Ya no enviamos información de días por modelo

      let result;
      if (isEditing) {
        // Actualizar oferta existente
        result = await ofertasService.update(oferta.id, formDataToSend);
      } else {
        // Crear nueva oferta
        result = await ofertasService.create(formDataToSend);
      }

      logger.info(`Oferta ${isEditing ? 'actualizada' : 'creada'} correctamente:`, result);

      // Llamar al callback onSubmit
      if (onSubmit) onSubmit(result);

    } catch (error) {
      logger.error(`Error al ${isEditing ? 'actualizar' : 'crear'} oferta:`, error);
      setErrors({
        submit: error.message || `Error al ${isEditing ? 'actualizar' : 'crear'} la oferta. Por favor, intenta de nuevo.`
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Ya no necesitamos la lista de días de la semana

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="bg-[#181828] p-8 rounded-2xl shadow-xl border border-[#29293d] max-w-2xl mx-auto">
        {/* Mostrar errores generales del formulario */}
        {errors.submit && (
          <div className="mb-6 p-4 bg-red-900/30 border border-red-800 rounded-lg">
            <p className="text-red-400 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Información básica */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
            <span className="text-[#9c27b0]">📝</span> Información de la Oferta
          </h3>
          <div className="bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner space-y-6">
            <div>
              <label className="block text-sm font-semibold text-gray-200 mb-1">Título <span className="text-pink-400">*</span></label>
              <input
                type="text"
                name="titulo"
                value={formData.titulo}
                onChange={handleChange}
                className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400 ${errors.titulo ? 'border-red-400' : 'border-[#29293d]'}`}
                placeholder="Ej: Promoción de Verano"
              />
              {errors.titulo && <p className="text-red-400 text-xs mt-1">{errors.titulo}</p>}
            </div>

            <div>
              <SeccionesDescripcion
                secciones={formData.secciones}
                onChange={handleSeccionesChange}
              />
              {errors.secciones && (
                <div className="mt-2">
                  {errors.secciones.map((error, index) => (
                    <p key={index} className="text-red-400 text-xs">{error}</p>
                  ))}
                </div>
              )}
              <p className="text-xs text-gray-400 mt-3 ml-1">
                Organiza la información de la oferta en secciones para una mejor presentación.
              </p>
            </div>

            {/* Botón de Visibilidad */}
            <div>
              <label className="block text-sm font-semibold text-gray-200 mb-1">Estado de la oferta</label>
              <button
                type="button"
                onClick={toggleVisible}
                className={`w-full flex items-center justify-between p-3 rounded-md border ${
                  formData.visible
                    ? 'bg-gradient-to-r from-[#4CAF50]/20 to-[#2E7D32]/20 text-white border-[#4CAF50] hover:from-[#4CAF50]/30 hover:to-[#2E7D32]/30'
                    : 'bg-gradient-to-r from-[#f44336]/20 to-[#d32f2f]/20 text-white border-[#f44336] hover:from-[#f44336]/30 hover:to-[#d32f2f]/30'
                } transition-all`}
              >
                <div className="flex items-center">
                  <span className="mr-2 text-lg">{formData.visible ? '🟢' : '🔴'}</span>
                  <span className="font-medium">{formData.visible ? 'Oferta activa' : 'Oferta inactiva'}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">
                    {formData.visible ? 'Visible para usuarios' : 'No visible para usuarios'}
                  </span>
                </div>
              </button>
              <p className="text-xs text-gray-400 mt-1 ml-1">
                {formData.visible
                  ? 'La oferta será visible en la página pública de ofertas.'
                  : 'La oferta no será visible para los usuarios hasta que la actives.'}
              </p>
            </div>
          </div>
        </div>



        {/* Botones de acción */}
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={() => navigate('/admin?tab=ofertas')}
            className="px-6 py-2 border border-[#29293d] rounded-lg text-gray-300 hover:bg-[#23233a] transition-colors"
            disabled={isSubmitting}
          >
            Cancelar
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-lg hover:opacity-90 transition-colors flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {isEditing ? 'Actualizando...' : 'Creando...'}
              </>
            ) : (
              isEditing ? 'Actualizar Oferta' : 'Crear Oferta'
            )}
          </button>
        </div>
      </div>
    </form>
  );
};

export default OfertaForm;
