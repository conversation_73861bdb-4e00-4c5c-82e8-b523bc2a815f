import React from 'react';

const UneteSection = () => {
  return (
    <section className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl overflow-hidden shadow-sm border border-pink-200">
      <div className="p-8">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-4 flex items-center justify-center text-center">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-500">Únete a Nuestras Brujitas</span>
          </h2>
          <p className="text-purple-800 mb-8 font-medium text-center max-w-2xl mx-auto">¿Quieres ser parte de la magia? 🧙‍♀️ En Brujitas Sexys Mx buscamos chicas atrevidas y sensuales que deseen brillar en el arte de la seducción 🔥. Únete a nuestro aquelarre y vive una experiencia llena de glamour, libertad y pasión 💋.</p>

          <div className="text-center">
            <p className="text-purple-800 mb-4 italic">¡Tu encanto puede ser el hechizo que alguien está esperando! 😈 ¿Lista para encantar? Chicas independientes y con servicio virtual, ¡No dudes en contactarnos!</p>
            <a href="https://wa.me/+527205373745" target="_blank" rel="noopener noreferrer" className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-pink-400 to-purple-400 text-white text-sm font-medium rounded-lg hover:shadow-sm hover:from-pink-300 hover:to-purple-300 transition-all duration-300">
              Tenemos una propuesta para ti
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default UneteSection;
