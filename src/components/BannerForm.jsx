import { useState, useEffect } from 'react';
import { bannerService } from '../services/bannerService';
import logger from '../services/logService';
import { API_URL } from '../config';

const BannerForm = ({ banner = null, onSubmit, onCancel }) => {
  const isEditing = !!banner;

  // Estado inicial del formulario
  const [formData, setFormData] = useState({
    titulo: '',
    descripcion: '',
    texto_resaltado: '',
    imagen: null,
    activo: true,
    orden: 0,
    whatsapp_texto: '',
    whatsapp_enlace: '',
    telegram_texto: '',
    telegram_enlace: '',
    custom_texto: '',
    custom_enlace: ''
  });

  // Estado para manejar errores de validación
  const [errors, setErrors] = useState({});

  // Estado para manejar la previsualización de la imagen
  const [imagePreview, setImagePreview] = useState(null);

  // Estado para manejar el envío del formulario
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Estado para almacenar los órdenes disponibles
  const [availableOrders, setAvailableOrders] = useState([]);

  // Cargar todos los banners para obtener los órdenes disponibles
  useEffect(() => {
    if (isEditing) {
      const fetchBanners = async () => {
        try {
          const response = await fetch(`${API_URL}/api/collections/banners/records?sort=orden`);
          if (response.ok) {
            const data = await response.json();

            // Crear un array con todos los órdenes existentes
            const orders = data.items.map(item => item.orden);

            // Si no hay banners, empezar con orden 1
            if (orders.length === 0) {
              setAvailableOrders([1]);
            } else {
              // Asegurarse de que el orden actual del banner que estamos editando esté incluido
              if (banner && banner.orden && !orders.includes(banner.orden)) {
                orders.push(banner.orden);
              }

              // Ordenar los órdenes numéricamente
              orders.sort((a, b) => a - b);

              setAvailableOrders(orders);
            }
          }
        } catch (err) {
          logger.error('Error al cargar órdenes disponibles:', err);
        }
      };

      fetchBanners();
    }
  }, [isEditing, banner]);

  // Cargar datos del banner si estamos editando
  useEffect(() => {
    if (isEditing && banner) {
      setFormData({
        titulo: banner.titulo || '',
        descripcion: banner.descripcion || '',
        texto_resaltado: banner.texto_resaltado || '',
        imagen: null,
        activo: banner.activo || false,
        orden: banner.orden || 0,
        whatsapp_texto: banner.whatsapp_texto || '',
        whatsapp_enlace: banner.whatsapp_enlace || '',
        telegram_texto: banner.telegram_texto || '',
        telegram_enlace: banner.telegram_enlace || '',
        custom_texto: banner.custom_texto || '',
        custom_enlace: banner.custom_enlace || ''
      });

      // Cargar previsualización de la imagen existente
      if (banner.imagen) {
        setImagePreview(bannerService.getImageUrl(banner, banner.imagen));
      }
    }
  }, [banner, isEditing]);

  // Manejar cambios en los campos del formulario
  const handleChange = (e) => {
    const { name, value, type, files, checked } = e.target;

    if (type === 'file') {
      // Para archivos
      const file = files[0];
      if (file) {
        setFormData(prev => ({
          ...prev,
          [name]: file
        }));

        // Crear previsualización para la imagen
        setImagePreview(URL.createObjectURL(file));
      }
    } else if (type === 'checkbox') {
      // Para checkboxes
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else if (type === 'number' || name === 'orden') {
      // Para campos numéricos y el select de orden
      setFormData(prev => ({
        ...prev,
        [name]: parseInt(value, 10) || 0
      }));
    } else {
      // Para campos de texto
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Función para alternar el estado activo
  const toggleActivo = () => {
    setFormData(prev => ({
      ...prev,
      activo: !prev.activo
    }));
  };

  // Función para eliminar la imagen
  const handleRemoveImage = () => {
    setImagePreview(null);
    setFormData(prev => ({
      ...prev,
      imagen: null
    }));
  };

  // Validar el formulario
  const validateForm = () => {
    const newErrors = {};

    if (!formData.titulo) newErrors.titulo = 'El título es obligatorio';

    // Si no estamos editando, la imagen es obligatoria
    if (!isEditing && !formData.imagen) {
      newErrors.imagen = 'La imagen es obligatoria';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manejar envío del formulario
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Crear un nuevo objeto FormData para enviar archivos
      const formDataToSend = new FormData();

      // Agregar campos de texto
      formDataToSend.append('titulo', formData.titulo);
      formDataToSend.append('descripcion', formData.descripcion);
      formDataToSend.append('texto_resaltado', formData.texto_resaltado);
      formDataToSend.append('activo', formData.activo);
      formDataToSend.append('orden', formData.orden);
      formDataToSend.append('whatsapp_texto', formData.whatsapp_texto);
      formDataToSend.append('whatsapp_enlace', formData.whatsapp_enlace);
      formDataToSend.append('telegram_texto', formData.telegram_texto);
      formDataToSend.append('telegram_enlace', formData.telegram_enlace);
      formDataToSend.append('custom_texto', formData.custom_texto);
      formDataToSend.append('custom_enlace', formData.custom_enlace);

      // Agregar imagen solo si se ha seleccionado
      if (formData.imagen) {
        formDataToSend.append('imagen', formData.imagen);
      }

      let result;
      if (isEditing) {
        result = await bannerService.update(banner.id, formDataToSend);
      } else {
        result = await bannerService.create(formDataToSend);
      }

      if (onSubmit) {
        onSubmit(result);
      }
    } catch (error) {
      logger.error('Error al guardar banner:', error);
      setErrors({
        submit: error.message || 'Error al guardar el banner. Por favor, intenta de nuevo.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="bg-[#181828] p-8 rounded-2xl shadow-xl border border-[#29293d] max-w-2xl mx-auto">
        {/* Información básica */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
            <span className="text-[#9c27b0]">📝</span> Información del Banner
          </h3>
          <div className="bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner space-y-6">
            <div>
              <label className="block text-sm font-semibold text-gray-200 mb-1">Título <span className="text-pink-400">*</span></label>
              <input
                type="text"
                name="titulo"
                value={formData.titulo}
                onChange={handleChange}
                className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400 ${errors.titulo ? 'border-red-400' : 'border-[#29293d]'}`}
                placeholder="Ej: Promoción Especial"
              />
              {errors.titulo && <p className="text-red-400 text-xs mt-1">{errors.titulo}</p>}
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-200 mb-1 flex items-center gap-2">
                <span className="text-[#e91e63]">✨</span> Texto Resaltado
              </label>
              <input
                type="text"
                name="texto_resaltado"
                value={formData.texto_resaltado}
                onChange={handleChange}
                className="w-full p-3 border border-[#29293d] rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400"
                placeholder="Texto que se mostrará con degradado de colores (opcional)"
              />
              <p className="text-xs text-gray-400 mt-1">Este texto se mostrará con un degradado de colores para destacarlo.</p>
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-200 mb-1">Descripción</label>
              <textarea
                name="descripcion"
                value={formData.descripcion}
                onChange={handleChange}
                rows="3"
                className="w-full p-3 border border-[#29293d] rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400"
                placeholder="Descripción del banner (opcional)"
              ></textarea>
            </div>

            {isEditing && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-200 mb-1">Orden</label>
                  <select
                    name="orden"
                    value={formData.orden}
                    onChange={handleChange}
                    className="w-full p-3 border border-[#29293d] rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white"
                  >
                    {availableOrders.map(order => (
                      <option key={order} value={order}>
                        {order} {banner && banner.orden === order ? '(posición actual)' : ''}
                      </option>
                    ))}
                  </select>
                  <p className="text-xs text-gray-400 mt-1">Selecciona la posición en la que quieres mostrar este banner</p>
                </div>
                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={toggleActivo}
                    className={`flex items-center justify-between p-3 rounded-md border w-full ${formData.activo ? 'bg-gradient-to-r from-[#4CAF50]/20 to-[#2E7D32]/20 text-white border-[#4CAF50]' : 'bg-gradient-to-r from-[#f44336]/20 to-[#d32f2f]/20 text-white border-[#f44336]'} transition-all`}
                  >
                    <div className="flex items-center">
                      <span className="mr-2 text-lg">{formData.activo ? '🟢' : '🔴'}</span>
                      <span className="font-medium">{formData.activo ? 'Activo' : 'Inactivo'}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">
                        {formData.activo ? 'Visible' : 'Oculto'}
                      </span>
                    </div>
                  </button>
                </div>
              </div>
            )}
            {!isEditing && (
              <div className="space-y-4">
                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={toggleActivo}
                    className={`flex items-center justify-between p-3 rounded-md border w-full ${formData.activo ? 'bg-gradient-to-r from-[#4CAF50]/20 to-[#2E7D32]/20 text-white border-[#4CAF50]' : 'bg-gradient-to-r from-[#f44336]/20 to-[#d32f2f]/20 text-white border-[#f44336]'} transition-all`}
                  >
                    <div className="flex items-center">
                      <span className="mr-2">{formData.activo ? '🟢' : '🔴'}</span>
                      <span>{formData.activo ? 'Activo' : 'Inactivo'}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">
                        {formData.activo ? 'Visible' : 'Oculto'}
                      </span>
                    </div>
                  </button>
                </div>
                <p className="text-sm text-gray-400 italic">El banner se le asignará automáticamente el siguiente número de orden disponible.</p>
              </div>
            )}
          </div>
        </div>

        {/* Imagen */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
            <span className="text-[#9c27b0]">🖼️</span> Imagen
          </h3>
          <div className="bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner">
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-200 flex items-center gap-1">
                <span className="text-[#e91e63]">🌟</span> Imagen del Banner {isEditing ? '(opcional)' : <span className="text-pink-400">*</span>}
              </label>
              <div className="relative border-2 border-dashed border-[#29293d] hover:border-[#e91e63] transition-colors rounded-lg p-4 text-center cursor-pointer bg-[#181828]">
                <input
                  type="file"
                  name="imagen"
                  accept="image/*"
                  onChange={handleChange}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                />
                {!imagePreview ? (
                  <div className="flex flex-col items-center justify-center py-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#e91e63] mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p className="text-sm text-gray-400">Haz clic para seleccionar o arrastra una imagen</p>
                  </div>
                ) : (
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="Vista previa"
                      className="max-h-40 mx-auto rounded"
                    />
                    <button
                      type="button"
                      onClick={handleRemoveImage}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                )}
              </div>
              {errors.imagen && <p className="text-red-400 text-xs mt-1">{errors.imagen}</p>}
            </div>
          </div>
        </div>

        {/* Botones de acción */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-[#e0b3ff] mb-3 flex items-center gap-2">
            <span className="text-[#9c27b0]">🔗</span> Botones de Acción
          </h3>
          <div className="bg-[#23233a] p-6 rounded-xl border border-[#29293d] shadow-inner space-y-6">
            {/* WhatsApp */}
            <div>
              <h4 className="text-md font-semibold text-[#25D366] mb-3 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z" />
                </svg>
                Botón de WhatsApp
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-200 mb-1">Texto del botón</label>
                  <input
                    type="text"
                    name="whatsapp_texto"
                    value={formData.whatsapp_texto}
                    onChange={handleChange}
                    className="w-full p-3 border border-[#29293d] rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400"
                    placeholder="Ej: Contactar por WhatsApp"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-200 mb-1">Enlace</label>
                  <input
                    type="text"
                    name="whatsapp_enlace"
                    value={formData.whatsapp_enlace}
                    onChange={handleChange}
                    className="w-full p-3 border border-[#29293d] rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400"
                    placeholder="Ej: https://wa.me/1234567890"
                  />
                </div>
              </div>
            </div>

            {/* Telegram */}
            <div>
              <h4 className="text-md font-semibold text-[#0088cc] mb-3 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
                </svg>
                Botón de Telegram
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-200 mb-1">Texto del botón</label>
                  <input
                    type="text"
                    name="telegram_texto"
                    value={formData.telegram_texto}
                    onChange={handleChange}
                    className="w-full p-3 border border-[#29293d] rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400"
                    placeholder="Ej: Contactar por Telegram"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-200 mb-1">Enlace</label>
                  <input
                    type="text"
                    name="telegram_enlace"
                    value={formData.telegram_enlace}
                    onChange={handleChange}
                    className="w-full p-3 border border-[#29293d] rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400"
                    placeholder="Ej: https://t.me/username"
                  />
                </div>
              </div>
            </div>

            {/* Botón Personalizado */}
            <div>
              <h4 className="text-md font-semibold text-[#9c27b0] mb-3 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" />
                </svg>
                Botón Personalizado
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-200 mb-1">Texto del botón</label>
                  <input
                    type="text"
                    name="custom_texto"
                    value={formData.custom_texto}
                    onChange={handleChange}
                    className="w-full p-3 border border-[#29293d] rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400"
                    placeholder="Ej: Más información"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-200 mb-1">Enlace</label>
                  <input
                    type="text"
                    name="custom_enlace"
                    value={formData.custom_enlace}
                    onChange={handleChange}
                    className="w-full p-3 border border-[#29293d] rounded-lg focus:ring-2 focus:ring-[#9c27b0] bg-[#23233a] text-white placeholder-gray-400"
                    placeholder="Ej: https://ejemplo.com"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Error general */}
        {errors.submit && (
          <div className="bg-[#2d0a16] p-4 rounded-lg border border-red-800 mb-6">
            <p className="text-red-400 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Botones */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 rounded-lg border border-[#29293d] bg-[#23233a] text-gray-300 hover:bg-[#29293d] transition-all flex items-center justify-center gap-2 shadow-md w-full"
            disabled={isSubmitting}
          >
            Cancelar
          </button>
          <button
            type="submit"
            className="px-6 py-3 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-lg hover:shadow-lg hover:shadow-[#9c27b0]/20 hover:translate-y-[-2px] active:translate-y-0 transition-all flex items-center justify-center gap-2 font-medium w-full disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Guardando...
              </>
            ) : (
              <>
                {isEditing ? (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Actualizar Banner
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Crear Banner
                  </>
                )}
              </>
            )}
          </button>
        </div>
      </div>
    </form>
  );
};

export default BannerForm;
