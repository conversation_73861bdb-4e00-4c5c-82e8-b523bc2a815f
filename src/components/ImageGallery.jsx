import { useState } from 'react';
import { modelosService } from '../services/pocketbase';
import { independientesService } from '../services/independientesService';
import logger from '../services/logService';
import LazyImage from './LazyImage';

const ImageGallery = ({ modelo }) => {
  const [selectedImage, setSelectedImage] = useState(null);

  // Verificar si hay galería y si es un array
  const hasGallery = modelo.galeria &&
                    (Array.isArray(modelo.galeria) ?
                     modelo.galeria.length > 0 :
                     modelo.galeria !== '');

  // Si no hay galería, mostrar mensaje
  if (!hasGallery) {
    logger.debug('No hay galería disponible:', modelo);
    return <div className="text-center py-8 text-gray-500">No hay imágenes en la galería</div>;
  }

  // Asegurarse de que galeria sea un array
  const galeriaArray = Array.isArray(modelo.galeria) ?
                      modelo.galeria :
                      [modelo.galeria];

  logger.debug('Galería array:', galeriaArray);

  // Obtener URLs de las imágenes
  const images = galeriaArray.map(img => {
    // Determinar qué servicio usar basado en la colección del modelo
    const service = modelo.collectionId === 'independientes' || modelo.collectionName === 'independientes'
      ? independientesService
      : modelosService;

    const url = service.getImageUrl(modelo, img);
    logger.debug(`URL para imagen ${img}:`, url);
    return {
      id: img,
      url: url
    };
  });

  // Abrir imagen en modal
  const openModal = (image) => {
    setSelectedImage(image);
  };

  // Cerrar modal
  const closeModal = () => {
    setSelectedImage(null);
  };

  return (
    <div>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {images.map((image) => (
          <div
            key={image.id}
            className="aspect-square overflow-hidden rounded-lg cursor-pointer"
            onClick={() => openModal(image)}
          >
            <LazyImage
              src={image.url}
              alt="Imagen de galería"
              className="w-full h-full hover:scale-110 transition-transform"
              objectFit="cover"
            />
          </div>
        ))}
      </div>

      {/* Modal para ver imagen ampliada */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" onClick={closeModal}>
          <div className="relative max-w-4xl w-full h-full flex items-center justify-center" onClick={e => e.stopPropagation()}>
            <LazyImage
              src={selectedImage.url}
              alt="Imagen ampliada"
              className="w-auto h-auto max-h-[90vh] max-w-[90vw] object-contain rounded-lg"
            />
            <button
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 hover:bg-opacity-75 rounded-full p-2 transition-all duration-300"
              onClick={closeModal}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGallery;
