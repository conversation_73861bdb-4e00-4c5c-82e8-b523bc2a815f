// Tema de colores para la aplicación
export const theme = {
  colors: {
    primary: {
      light: '#9c27b0', // Morado claro
      main: '#6a0dad',  // Morado principal
      dark: '#4a148c',  // Morado oscuro
      contrastText: '#ffffff'
    },
    secondary: {
      light: '#ff4081', // <PERSON> claro
      main: '#f50057',  // <PERSON> principal
      dark: '#c51162',  // Rosa oscuro
      contrastText: '#ffffff'
    },
    background: {
      default: '#121212', // Fondo oscuro
      paper: '#1e1e1e',   // Fondo de tarjetas
      light: '#2d2d2d'    // Fondo más claro
    },
    text: {
      primary: '#ffffff',   // Texto principal
      secondary: '#b0b0b0', // Texto secundario
      disabled: '#6c6c6c'   // Texto deshabilitado
    },
    accent: {
      purple: '#9c27b0',
      pink: '#e91e63',
      blue: '#2196f3',
      green: '#4caf50',
      yellow: '#ffeb3b',
      orange: '#ff9800'
    },
    status: {
      vip: '#ffd700',      // Dorado para VIP
      destacado: '#ff4081' // Rosa para destacado
    }
  },
  shadows: {
    small: '0 2px 8px rgba(0, 0, 0, 0.15)',
    medium: '0 4px 12px rgba(0, 0, 0, 0.2)',
    large: '0 8px 24px rgba(0, 0, 0, 0.25)',
    glow: '0 0 15px rgba(156, 39, 176, 0.5)' // Brillo morado
  },
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '16px',
    circle: '50%'
  },
  transitions: {
    fast: '0.2s ease',
    medium: '0.3s ease',
    slow: '0.5s ease'
  }
};

// Estilos comunes reutilizables
export const commonStyles = {
  card: {
    base: 'bg-[#1e1e1e] rounded-lg overflow-hidden shadow-lg transition-transform hover:scale-105 hover:shadow-xl border border-[#3d3d3d]',
    hover: 'hover:shadow-[0_0_15px_rgba(156,39,176,0.5)]'
  },
  button: {
    primary: 'bg-[#6a0dad] hover:bg-[#9c27b0] text-white font-medium py-2 px-4 rounded-md transition-colors',
    secondary: 'bg-transparent border border-[#6a0dad] text-[#9c27b0] hover:bg-[#6a0dad] hover:text-white font-medium py-2 px-4 rounded-md transition-colors',
    danger: 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors'
  },
  input: 'bg-[#2d2d2d] text-white border border-[#3d3d3d] rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#9c27b0] focus:border-transparent',
  badge: {
    vip: 'bg-[#ffd700] bg-opacity-20 text-[#ffd700] px-2 py-1 rounded-full text-xs font-semibold',
    destacado: 'bg-[#ff4081] bg-opacity-20 text-[#ff4081] px-2 py-1 rounded-full text-xs font-semibold'
  },
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  section: 'py-6 md:py-10',
  heading: {
    h1: 'text-3xl md:text-4xl font-bold text-white',
    h2: 'text-2xl md:text-3xl font-bold text-white',
    h3: 'text-xl md:text-2xl font-semibold text-white',
    h4: 'text-lg md:text-xl font-semibold text-white'
  },
  text: {
    body: 'text-[#b0b0b0]',
    small: 'text-sm text-[#b0b0b0]'
  }
};

export default theme;
