import { createBrowserRouter, Navigate, Outlet } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import App from './App';

// Componente de carga para usar con Suspense
import LoadingSpinner from './components/LoadingSpinner';

// Importación de páginas con lazy loading
const HomePage = lazy(() => import('./pages/HomePage'));
const LoginPage = lazy(() => import('./pages/LoginPage'));
const AdminPage = lazy(() => import('./pages/AdminPage'));
const CreateModeloPage = lazy(() => import('./pages/CreateModeloPage'));
const EditModeloPage = lazy(() => import('./pages/EditModeloPage'));
const CreateBannerPage = lazy(() => import('./pages/CreateBannerPage'));
const EditBannerPage = lazy(() => import('./pages/EditBannerPage'));
const ModeloDetailPage = lazy(() => import('./pages/ModeloDetailPage'));
const BrujitaDetailPage = lazy(() => import('./pages/BrujitaDetailPage'));
const WelcomePage = lazy(() => import('./pages/WelcomePage'));
// Import Independiente Pages
const CreateIndependientePage = lazy(() => import('./pages/CreateIndependientePage'));
const EditIndependientePage = lazy(() => import('./pages/EditIndependientePage'));
const IndependienteDetailPage = lazy(() => import('./pages/IndependienteDetailPage'));
const IndependientesPage = lazy(() => import('./pages/IndependientesPage'));
const ClientesVipPage = lazy(() => import('./pages/ClientesVipPage'));
// Import Ofertas Pages
const OfertasPage = lazy(() => import('./pages/OfertasPage'));
const CreateOfertaPage = lazy(() => import('./pages/CreateOfertaPage'));
const EditOfertaPage = lazy(() => import('./pages/EditOfertaPage'));

/**
 * Función para envolver componentes con ErrorBoundary y Suspense
 * Suspense muestra el LoadingSpinner mientras se carga el componente lazy
 */
const withErrorBoundary = (component) => (
  <ErrorBoundary>
    <Suspense fallback={<LoadingSpinner />}>
      {component}
    </Suspense>
  </ErrorBoundary>
);

/**
 * Configuración de rutas de la aplicación
 * Las rutas administrativas están protegidas y requieren autenticación
 * Todas las rutas están envueltas en un ErrorBoundary para capturar errores
 */
const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        path: '',
        element: withErrorBoundary(<HomePage />),
      },
      {
        path: 'bienvenida',
        element: withErrorBoundary(<WelcomePage />),
      },
      {
        path: 'welcome',
        element: withErrorBoundary(
          <Navigate to="/bienvenida" replace />
        ),
      },
      {
        path: 'login',
        element: withErrorBoundary(<LoginPage />),
      },
      {
        path: 'modelo/:id',
        element: withErrorBoundary(<ModeloDetailPage />),
      },
      {
        path: 'brujita/:id',
        element: withErrorBoundary(<BrujitaDetailPage />),
      },
      {
        path: 'independientes',
        element: withErrorBoundary(<IndependientesPage />),
      },
      {
        path: 'independiente/:id', // New route for Independiente detail
        element: withErrorBoundary(<IndependienteDetailPage />),
      },
      {
        path: 'clientesvip',
        element: withErrorBoundary(<ClientesVipPage />),
      },
      {
        path: 'ofertas',
        element: withErrorBoundary(<OfertasPage />),
      },
      // Rutas protegidas que requieren autenticación
      {
        path: 'admin',
        element: withErrorBoundary(
          <ProtectedRoute>
            <AdminPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/nueva-brujita',
        element: withErrorBoundary(
          <ProtectedRoute>
            <CreateModeloPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/editar-brujita/:id',
        element: withErrorBoundary(
          <ProtectedRoute>
            <EditModeloPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/nueva-independiente', // New route for creating Independiente
        element: withErrorBoundary(
          <ProtectedRoute>
            <CreateIndependientePage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/editar-independiente/:id', // New route for editing Independiente
        element: withErrorBoundary(
          <ProtectedRoute>
            <EditIndependientePage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/nuevo-banner',
        element: withErrorBoundary(
          <ProtectedRoute>
            <CreateBannerPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/editar-banner/:id',
        element: withErrorBoundary(
          <ProtectedRoute>
            <EditBannerPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/nueva-oferta',
        element: withErrorBoundary(
          <ProtectedRoute>
            <CreateOfertaPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/editar-oferta/:id',
        element: withErrorBoundary(
          <ProtectedRoute>
            <EditOfertaPage />
          </ProtectedRoute>
        ),
      },
      // Ruta para manejar páginas no encontradas
      {
        path: '*',
        element: withErrorBoundary(
          <div className="min-h-screen bg-[#121220] text-white flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-[#181828] p-8 rounded-lg shadow-lg border border-[#29293d] text-center">
              <h1 className="text-3xl font-bold text-[#e0b3ff] mb-4">404</h1>
              <p className="text-xl text-gray-300 mb-6">Página no encontrada</p>
              <a
                href="/"
                className="px-6 py-3 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-lg inline-block hover:opacity-90 transition-all"
              >
                Volver al inicio
              </a>
            </div>
          </div>
        ),
      },
    ]
  },
]);

export default router;
