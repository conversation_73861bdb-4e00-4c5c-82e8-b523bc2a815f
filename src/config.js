/**
 * Configuración centralizada de la aplicación
 * Permite gestionar variables de entorno y configuraciones globales
 */

// URL base de la API
export const API_URL = 'https://db.brujitassexysmx.com';

// Configuración de seguridad
export const SECURITY_CONFIG = {
  // Tiempo de expiración de la sesión en milisegundos (30 minutos)
  SESSION_EXPIRY: 30 * 60 * 1000,

  // Número máximo de intentos de inicio de sesión antes de bloqueo
  MAX_LOGIN_ATTEMPTS: 5,

  // Tiempo de bloqueo después de exceder los intentos máximos (15 minutos)
  LOCKOUT_TIME: 15 * 60 * 1000,

  // Patrones sospechosos para detección de XSS
  XSS_PATTERNS: [
    /javascript:/i,
    /\<script\>/i,
    /onerror=/i,
    /onload=/i,
    /onclick=/i,
    /alert\(/i,
    /eval\(/i,
    /document\.cookie/i,
    /\<iframe/i
  ]
};

// Configuración de la aplicación
export const APP_CONFIG = {
  // Nombre de la aplicación
  APP_NAME: 'Brujitas',

  // Versión de la aplicación
  VERSION: '1.0.0',

  // Modo de desarrollo
  IS_DEV: process.env.NODE_ENV === 'development',

  // Habilitar logs detallados (desactivado para producción)
  VERBOSE_LOGGING: false,

  // Nivel de logs (0: ninguno, 1: solo errores, 2: errores y advertencias, 3: todos)
  LOG_LEVEL: process.env.NODE_ENV === 'production' ? 0 : 1
};

// Exportar configuración completa como objeto
export default {
  API_URL,
  SECURITY_CONFIG,
  APP_CONFIG
};
