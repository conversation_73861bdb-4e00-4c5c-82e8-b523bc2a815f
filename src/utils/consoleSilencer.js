/**
 * Silencia mensajes específicos de la consola para producción
 * Esta utilidad sobrescribe los métodos de console para filtrar mensajes no deseados
 * y también intercepta errores de red para evitar que se muestren en la consola
 */

// Solución radical para silenciar inmediatamente cualquier mensaje relacionado con el ID problemático
// Esta función se ejecuta inmediatamente antes de cualquier otra lógica
(function() {
  // Guardar las implementaciones originales de console
  const originalConsoleError = console.error;
  const originalConsoleLog = console.log;
  const originalConsoleWarn = console.warn;
  
  // Reemplazar console.error para bloquear inmediatamente el mensaje problemático
  console.error = function(...args) {
    if (args && args.length > 0) {
      const message = String(args[0]);
      if (message.includes('z7b83n2e8a') || 
          (message.includes('brujitas') && message.includes('404')) ||
          message.includes('GET') && message.includes('brujitas/records') ||
          message.includes('db.brujitassexysmx.com/api/collections/brujitas/records')) {
        return; // Bloquear completamente este mensaje
      }
    }
    originalConsoleError.apply(console, args);
  };
  
  // También reemplazar console.log para el mismo propósito
  console.log = function(...args) {
    if (args && args.length > 0) {
      const message = String(args[0]);
      if (message.includes('z7b83n2e8a') || 
          (message.includes('brujitas') && message.includes('404')) ||
          message.includes('GET') && message.includes('brujitas/records') ||
          message.includes('db.brujitassexysmx.com/api/collections/brujitas/records')) {
        return; // Bloquear completamente este mensaje
      }
    }
    originalConsoleLog.apply(console, args);
  };
  
  // También reemplazar console.warn para el mismo propósito
  console.warn = function(...args) {
    if (args && args.length > 0) {
      const message = String(args[0]);
      if (message.includes('z7b83n2e8a') || 
          (message.includes('brujitas') && message.includes('404')) ||
          message.includes('GET') && message.includes('brujitas/records') ||
          message.includes('db.brujitassexysmx.com/api/collections/brujitas/records')) {
        return; // Bloquear completamente este mensaje
      }
    }
    originalConsoleWarn.apply(console, args);
  };
})();

// Lista de patrones de mensajes a silenciar
const SILENCED_MESSAGES = [
  'MutationObserver started',
  'MutationObserver stopped',
  'content.js',
  '[HMR]', // Hot Module Replacement logs
  '[vite]', // Vite logs
  'Download the React DevTools', // React DevTools mensaje
  '404 (Not Found)', // Errores 404
  'Error al obtener modelo con ID', // Errores de modelo no encontrado
  'Error al cargar la brujita', // Errores de brujita no encontrada
  'Failed to load resource', // Errores de carga de recursos
  'GET', // Peticiones GET
  'POST', // Peticiones POST
  'PUT', // Peticiones PUT
  'DELETE', // Peticiones DELETE
  'api/collections/brujitas/records', // Cualquier petición a la API de brujitas
  'db.brujitassexysmx.com/api/collections/brujitas/records', // URL específica de la API de brujitas
  'GET `https://db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a`', // Mensaje exacto sin el 404
  'GET `https://db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a`  404 (Not Found)', // Mensaje exacto que está causando problemas
  'brujitas/records/z7b83n2e8a', // ID específico que causa problemas
  'z7b83n2e8a', // ID específico aislado
  'GET https://db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a', // Variante sin comillas invertidas
  'GET https://db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a 404', // Variante sin comillas y con 404
  'GET https://db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a 404 (Not Found)' // Variante completa sin comillas
];

// Función para interceptar y silenciar mensajes a nivel global
(function() {
  // Guardar la implementación original de console.error
  const originalConsoleError = window.console.error;
  
  // Reemplazar console.error a nivel global
  window.console.error = function(...args) {
    if (args && args.length > 0) {
      const message = String(args[0]);
      // Verificación más exhaustiva para capturar todas las variantes del mensaje
      if (message.includes('db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a') ||
          (message.includes('404') && message.includes('brujitas')) ||
          (message.includes('GET') && message.includes('brujitas/records')) ||
          message.includes('z7b83n2e8a')) {
        return; // No mostrar este mensaje específico
      }
    }
    originalConsoleError.apply(console, args);
  };
})();

// Lista de URLs específicas a silenciar completamente
const SILENCED_URLS = [
  'db.brujitassexysmx.com/api/collections/brujitas/records',
  'db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a', // URL específica que está causando problemas
  'https://db.brujitassexysmx.com/api/collections/brujitas/records',
  'https://db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a' // Versiones con https explícito
];

// Función para verificar si un mensaje debe ser silenciado
const shouldSilenceMessage = (args) => {
  if (!args || args.length === 0) return false;

  const message = String(args[0]);
  
  // Verificación específica para el ID problemático (prioridad máxima)
  if (message.includes('z7b83n2e8a')) {
    return true;
  }
  
  // Verificación específica para errores 404 de la API de brujitas (más agresiva)
  if (message.includes('404') && 
      (message.includes('api/collections/brujitas/records') || 
       message.includes('db.brujitassexysmx.com/api/collections/brujitas/records') ||
       message.includes('brujitas'))) {
    return true;
  }
  
  // Verificación específica para cualquier petición a la API de brujitas
  if ((message.includes('GET') || message.includes('POST') || message.includes('PUT') || message.includes('DELETE')) && 
      (message.includes('api/collections/brujitas/records') || 
       message.includes('db.brujitassexysmx.com/api/collections/brujitas/records') ||
       message.includes('brujitas/records'))) {
    return true;
  }
  
  // Verificar si el mensaje contiene alguna URL específica a silenciar completamente
  if (SILENCED_URLS.some(url => message.includes(url))) {
    return true;
  }
  
  // Verificación para cualquier mensaje que contenga la palabra brujitas y algún tipo de error
  if (message.includes('brujitas') && 
      (message.includes('error') || message.includes('Error') || 
       message.includes('404') || message.includes('Not Found'))) {
    return true;
  }
  
  return SILENCED_MESSAGES.some(pattern => message.includes(pattern));
};

// Guardar las funciones originales de console
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  debug: console.debug
};

// Interceptar errores de red
const interceptNetworkErrors = () => {
  // Guardar la implementación original de fetch
  const originalFetch = window.fetch;

  // Sobrescribir fetch para interceptar errores
  window.fetch = async function(input, init) {
    // Verificar si la URL contiene alguno de los patrones a silenciar
    const url = typeof input === 'string' ? input : (input && input.url ? input.url : '');
    
    // Verificar si la URL debe ser silenciada completamente (URLs específicas)
    const shouldSilenceCompletely = SILENCED_URLS.some(pattern => url && url.includes(pattern));
    
    // Verificar si la URL contiene alguno de los patrones generales a silenciar
    const shouldSilence = shouldSilenceCompletely || SILENCED_MESSAGES.some(pattern => url && url.includes(pattern));
    
    // Para URLs que deben ser silenciadas, solo silenciamos los mensajes de consola pero permitimos la petición
    // Verificación más específica para la URL problemática
    if (url && (url.includes('db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a') || url.includes('db.brujitassexysmx.com/api/collections/brujitas/records'))) {
      // Solo silenciar los mensajes de consola temporalmente, pero permitir que la petición continúe
      const tempConsoleError = console.error;
      const tempConsoleLog = console.log;
      console.error = function() {}; // Silenciar console.error temporalmente
      console.log = function() {}; // Silenciar console.log temporalmente
      
      // Restaurar después de un breve tiempo
      setTimeout(() => {
        console.error = tempConsoleError;
        console.log = tempConsoleLog;
      }, 100);
      
      // Continuamos con la petición normal en lugar de bloquearla
      // No retornamos nada aquí para permitir que la petición continúe
    }

    try {
      // Realizar la petición original
      const response = await originalFetch.apply(this, arguments);
      
      // Si la URL debe ser silenciada y es un 404, solo silenciamos los mensajes de error
      // pero mantenemos la respuesta original para que la aplicación pueda manejarla correctamente
      if ((shouldSilence || url.includes('db.brujitassexysmx.com/api/collections/brujitas/records')) && response.status === 404) {
        // Silenciamos TODOS los métodos de consola temporalmente para este tipo de errores
        const tempConsoleMethods = {};
        Object.keys(originalConsole).forEach(method => {
          tempConsoleMethods[method] = console[method];
          console[method] = function() {};
        });
        
        // Restauramos después de un breve tiempo
        setTimeout(() => {
          Object.keys(originalConsole).forEach(method => {
            console[method] = tempConsoleMethods[method];
          });
        }, 1000);
        
        // Devolvemos la respuesta original sin modificar
        return response;
      }
      
      return response;
    } catch (error) {
      // Si la URL debe ser silenciada, silenciamos los mensajes de error pero permitimos que el error se propague
      // para que la aplicación pueda manejarlo correctamente
      if (shouldSilence) {
        // Silenciamos temporalmente los mensajes de consola
        const tempConsoleMethods = {};
        Object.keys(originalConsole).forEach(method => {
          tempConsoleMethods[method] = console[method];
          console[method] = function() {};
        });
        
        // Restauramos después de un breve tiempo
        setTimeout(() => {
          Object.keys(originalConsole).forEach(method => {
            console[method] = tempConsoleMethods[method];
          });
        }, 1000);
      }
      
      // Propagamos el error para que la aplicación pueda manejarlo

      // Si no debe ser silenciada, propagar el error
      throw error;
    }
  };
};

// Función para silenciar inmediatamente mensajes específicos
const silenceSpecificMessages = () => {
  // Silenciar temporalmente todos los métodos de consola para mensajes específicos
  const tempConsoleMethods = {};
  Object.keys(originalConsole).forEach(method => {
    tempConsoleMethods[method] = console[method];
    console[method] = function(...args) {
      if (args && args.length > 0) {
        const message = String(args[0]);
        // Verificación específica para la URL problemática - versión más agresiva
        if (message.includes('db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a') || 
            message.includes('GET `https://db.brujitassexysmx.com/api/collections/brujitas/records') ||
            (message.includes('404') && message.includes('brujitas')) ||
            (message.includes('GET') && message.includes('404') && message.includes('Not Found')) ||
            (message.includes('404 (Not Found)') && message.includes('brujitas')) ||
            message.includes('api/collections/brujitas/records')) {
          return; // No mostrar el mensaje
        }
      }
      tempConsoleMethods[method].apply(console, args);
    };
  });
  
  // Restaurar después de un tiempo más largo para cubrir la carga inicial
  setTimeout(() => {
    Object.keys(originalConsole).forEach(method => {
      console[method] = tempConsoleMethods[method];
    });
  }, 5000);
  
  // Configurar un MutationObserver para detectar y eliminar mensajes de error en la consola del DOM
  // Esto es útil para navegadores que muestran errores en elementos del DOM
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
          for (let i = 0; i < mutation.addedNodes.length; i++) {
            const node = mutation.addedNodes[i];
            // Verificar si el nodo contiene mensajes de error relacionados con brujitas
            if (node.textContent && 
                (node.textContent.includes('db.brujitassexysmx.com/api/collections/brujitas/records') ||
                 node.textContent.includes('404') && node.textContent.includes('brujitas'))) {
              // Eliminar el nodo que contiene el mensaje de error
              if (node.parentNode) {
                node.parentNode.removeChild(node);
              }
            }
          }
        }
      });
    });
    
    // Observar cambios en el cuerpo del documento
    observer.observe(document.body || document.documentElement, {
      childList: true,
      subtree: true
    });
  }
};

// Función para inicializar el silenciador de consola
export const initConsoleSilencer = () => {
  // Aplicar siempre en producción, pero también se puede activar en desarrollo
  const shouldApply = process.env.NODE_ENV === 'production' || true;
  
  // Solución radical para el mensaje específico que está causando problemas
  // Sobrescribir completamente los métodos de consola antes de cualquier otra operación
  const originalConsoleMethodsBackup = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
    debug: console.debug
  };
  
  // Reemplazar todos los métodos de consola con versiones que filtran el mensaje problemático
  Object.keys(originalConsoleMethodsBackup).forEach(method => {
    console[method] = function(...args) {
      if (args && args.length > 0) {
        const message = String(args[0]);
        if (message.includes('GET `https://db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a`') ||
            (message.includes('404') && message.includes('brujitas')) ||
            message.includes('db.brujitassexysmx.com/api/collections/brujitas/records')) {
          return; // Bloquear completamente este mensaje
        }
      }
      originalConsoleMethodsBackup[method].apply(console, args);
    };
  });
  

  if (!shouldApply) return;
  
  // Parche específico para el error que está apareciendo
  // Esto silencia directamente el mensaje exacto que está causando problemas
  const originalConsoleError = console.error;
  console.error = function(...args) {
    if (args && args.length > 0) {
      const message = String(args[0]);
      // Verificación más agresiva para el mensaje específico
      if (message.includes('GET `https://db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a`') || 
          message.includes('404 (Not Found)') && message.includes('brujitas') || 
          message.includes('db.brujitassexysmx.com/api/collections/brujitas/records')) {
        // Silenciar completamente este mensaje específico
        return;
      }
    }
    originalConsoleError.apply(console, args);
  };
  
  // Sobrescribir también console.log para el mismo mensaje
  const originalConsoleLog = console.log;
  console.log = function(...args) {
    if (args && args.length > 0) {
      const message = String(args[0]);
      // Verificación más agresiva para el mensaje específico
      if (message.includes('GET `https://db.brujitassexysmx.com/api/collections/brujitas/records/z7b83n2e8a`') || 
          message.includes('404 (Not Found)') && message.includes('brujitas') || 
          message.includes('db.brujitassexysmx.com/api/collections/brujitas/records')) {
        // Silenciar completamente este mensaje específico
        return;
      }
    }
    originalConsoleLog.apply(console, args);
  };
  
  // Silenciar inmediatamente mensajes específicos (antes de cualquier otra configuración)
  silenceSpecificMessages();

  // Interceptar errores de red
  interceptNetworkErrors();

  // Sobrescribir console.log
  console.log = function(...args) {
    if (!shouldSilenceMessage(args)) {
      originalConsole.log.apply(console, args);
    }
  };

  // Sobrescribir console.info
  console.info = function(...args) {
    if (!shouldSilenceMessage(args)) {
      originalConsole.info.apply(console, args);
    }
  };

  // Sobrescribir console.warn
  console.warn = function(...args) {
    if (!shouldSilenceMessage(args)) {
      originalConsole.warn.apply(console, args);
    }
  };

  // Sobrescribir console.error - versión más agresiva
  console.error = function(...args) {
    // Verificación directa para el mensaje problemático antes de cualquier otra lógica
    if (args && args.length > 0) {
      const message = String(args[0]);
      if (message.includes('z7b83n2e8a') || 
          (message.includes('brujitas') && message.includes('404')) ||
          message.includes('db.brujitassexysmx.com/api/collections/brujitas/records')) {
        return; // Bloquear inmediatamente sin procesar más
      }
    }
    
    // Verificación general para otros mensajes
    if (!shouldSilenceMessage(args)) {
      originalConsole.error.apply(console, args);
    }
  };

  // Sobrescribir console.debug
  console.debug = function(...args) {
    if (!shouldSilenceMessage(args)) {
      originalConsole.debug.apply(console, args);
    }
  };

  // Interceptar errores no capturados - versión ultra agresiva
  window.addEventListener('error', function(event) {
    // Verificación inmediata para el ID problemático (máxima prioridad)
    if (event && (event.message || event.filename || (event.error && event.error.message))) {
      const messageToCheck = event.message || event.filename || (event.error && event.error.message) || '';
      
      // Verificar si contiene el ID específico que causa problemas
      if (messageToCheck.includes('z7b83n2e8a')) {
        // Silenciar TODOS los métodos de consola inmediatamente
        Object.keys(originalConsole).forEach(method => {
          console[method] = function() {};
        });
        
        // Restaurar después de un tiempo más largo
        setTimeout(() => {
          Object.keys(originalConsole).forEach(method => {
            console[method] = originalConsole[method];
          });
        }, 3000);
        
        // Prevenir que el error se muestre en la consola
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
      
      // Verificación para cualquier error relacionado con brujitas
      if (messageToCheck.includes('brujitas') || 
          messageToCheck.includes('api/collections/brujitas') || 
          messageToCheck.includes('db.brujitassexysmx.com') || 
          (messageToCheck.includes('404') && messageToCheck.includes('GET')) || 
          (messageToCheck.includes('Not Found') && messageToCheck.includes('brujitas'))) {
        // Silenciar TODOS los métodos de consola
        Object.keys(originalConsole).forEach(method => {
          console[method] = function() {};
        });
        
        // Restaurar después de un tiempo más largo
        setTimeout(() => {
          Object.keys(originalConsole).forEach(method => {
            console[method] = originalConsole[method];
          });
        }, 3000);
        
        // Prevenir que el error se muestre en la consola
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    }
    
    // Verificación general usando shouldSilenceMessage
    if (event && event.message && shouldSilenceMessage([event.message])) {
      // Prevenir que el error se muestre en la consola
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
    
    // Verificación específica para errores de red relacionados con brujitas
    if (event && event.filename) {
      const filename = String(event.filename);
      if (filename.includes('api/collections/brujitas/records') || 
          filename.includes('db.brujitassexysmx.com/api/collections/brujitas/records') ||
          filename.includes('brujitas')) {
        // Prevenir que el error se muestre en la consola
        event.preventDefault();
        event.stopPropagation();
        
        // Silenciar todos los métodos de consola
        Object.keys(originalConsole).forEach(method => {
          console[method] = function() {};
        });
        
        // Restaurar después de un tiempo más largo
        setTimeout(() => {
          Object.keys(originalConsole).forEach(method => {
            console[method] = originalConsole[method];
          });
        }, 3000);
        
        return false;
      }
    }
  }, true);

  // Interceptar promesas rechazadas no capturadas - versión ultra agresiva
  window.addEventListener('unhandledrejection', function(event) {
    // Verificación inmediata para el ID problemático
    if (event && event.reason) {
      // Convertir a string para facilitar la búsqueda
      const reasonStr = typeof event.reason === 'object' ? JSON.stringify(event.reason) : String(event.reason);
      
      // Verificación específica para el ID problemático (máxima prioridad)
      if (reasonStr.includes('z7b83n2e8a')) {
        // Silenciar TODOS los métodos de consola inmediatamente
        Object.keys(originalConsole).forEach(method => {
          console[method] = function() {};
        });
        
        // Restaurar después de un tiempo más largo para asegurar que no se muestre nada
        setTimeout(() => {
          Object.keys(originalConsole).forEach(method => {
            console[method] = originalConsole[method];
          });
        }, 3000);
        
        // Prevenir que el error se muestre en la consola
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
      
      // Verificación para cualquier error relacionado con brujitas
      if (reasonStr.includes('brujitas') || 
          reasonStr.includes('api/collections/brujitas') || 
          reasonStr.includes('db.brujitassexysmx.com') || 
          (reasonStr.includes('404') && (reasonStr.includes('GET') || reasonStr.includes('brujitas'))) || 
          (reasonStr.includes('Not Found') && reasonStr.includes('brujitas'))) {
        // Silenciar TODOS los métodos de consola
        Object.keys(originalConsole).forEach(method => {
          console[method] = function() {};
        });
        
        // Restaurar después de un tiempo más largo
        setTimeout(() => {
          Object.keys(originalConsole).forEach(method => {
            console[method] = originalConsole[method];
          });
        }, 3000);
        
        // Prevenir que el error se muestre en la consola
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
      
      // Verificación general usando shouldSilenceMessage
      if (shouldSilenceMessage([reasonStr])) {
        // Silenciar temporalmente todos los métodos de consola
        Object.keys(originalConsole).forEach(method => {
          console[method] = function() {};
        });
        
        // Restaurar después de un tiempo
        setTimeout(() => {
          Object.keys(originalConsole).forEach(method => {
            console[method] = originalConsole[method];
          });
        }, 2000);
        
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    }
  }, true);
  
  // Sobrescribir el método XMLHttpRequest para interceptar errores de red - versión mejorada
  const originalXHROpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function(method, url, ...args) {
    // Verificación más específica para la URL problemática
    if (url && typeof url === 'string' && 
        (SILENCED_URLS.some(pattern => url.includes(pattern)) || 
         url.includes('db.brujitassexysmx.com/api/collections/brujitas/records') ||
         url.includes('brujitas/records') ||
         url.includes('z7b83n2e8a'))) {
      
      // Silenciar preventivamente cualquier mensaje relacionado con esta URL
      const tempConsoleMethods = {};
      Object.keys(originalConsole).forEach(method => {
        tempConsoleMethods[method] = console[method];
        // Silenciar inmediatamente para evitar cualquier mensaje
        console[method] = function() {};
      });
      
      // Para la URL específica que causa problemas, ya no cancelamos la solicitud
      // Solo silenciamos los mensajes de error pero permitimos que la solicitud continúe
      if (url.includes('brujitas/records/z7b83n2e8a')) {
        setTimeout(() => {
          // Restaurar los métodos de consola después
          Object.keys(originalConsole).forEach(method => {
            console[method] = tempConsoleMethods[method];
          });
        }, 2000);
        
        // Permitimos que la solicitud continúe normalmente
      }
      
      // Modificar el comportamiento para URLs silenciadas
      this.addEventListener('readystatechange', function() {
        if (this.readyState === 4) {
          // Silenciar TODOS los métodos de consola para cualquier respuesta de esta URL
          Object.keys(originalConsole).forEach(method => {
            console[method] = function() {};
          });
          
          // Restaurar después de un tiempo más largo
          setTimeout(() => {
            Object.keys(originalConsole).forEach(method => {
              console[method] = tempConsoleMethods[method];
            });
          }, 2000);
        }
      });
      
      // También silenciar cualquier error durante la carga
      this.addEventListener('error', function(event) {
        event.preventDefault();
        return false;
      });
    }
    
    return originalXHROpen.apply(this, [method, url, ...args]);
  };
};

// Función para restaurar la consola original
export const restoreConsole = () => {
  console.log = originalConsole.log;
  console.info = originalConsole.info;
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
  console.debug = originalConsole.debug;
};

export default {
  init: initConsoleSilencer,
  restore: restoreConsole
};
