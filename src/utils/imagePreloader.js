/**
 * Utilidad para precargar imágenes críticas
 * Esto mejora la experiencia del usuario al cargar imágenes importantes antes de que se necesiten
 */

/**
 * Precarga una imagen y devuelve una promesa que se resuelve cuando la imagen está cargada
 * @param {string} src - URL de la imagen a precargar
 * @returns {Promise} - Promesa que se resuelve cuando la imagen está cargada
 */
export const preloadImage = (src) => {
  return new Promise((resolve, reject) => {
    if (!src) {
      reject(new Error('No se proporcionó URL de imagen'));
      return;
    }

    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error(`Error al cargar la imagen: ${src}`));
    img.src = src;
  });
};

/**
 * Precarga un array de imágenes y devuelve una promesa que se resuelve cuando todas están cargadas
 * @param {Array<string>} srcArray - Array de URLs de imágenes a precargar
 * @returns {Promise} - Promesa que se resuelve cuando todas las imágenes están cargadas
 */
export const preloadImages = (srcArray) => {
  if (!Array.isArray(srcArray) || srcArray.length === 0) {
    return Promise.resolve([]);
  }

  const promises = srcArray.map(src => preloadImage(src));
  return Promise.all(promises);
};

/**
 * Precarga las imágenes críticas de la aplicación
 * @param {Array<string>} additionalImages - Imágenes adicionales a precargar
 */
export const preloadCriticalImages = (additionalImages = []) => {
  // Imágenes críticas que siempre deben precargarse
  const criticalImages = [
    '/img/placeholder.png',
    // Añadir otras imágenes críticas aquí
  ];

  // Combinar con imágenes adicionales
  const allImages = [...criticalImages, ...additionalImages];

  // Precargar todas las imágenes
  return preloadImages(allImages);
};

export default {
  preloadImage,
  preloadImages,
  preloadCriticalImages
};
