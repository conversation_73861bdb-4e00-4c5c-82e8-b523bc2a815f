// src/utils/adminUtils.js
import logger from '../services/logService';

export const isEntityVIP = (entity) => {
  if (!entity) return false;
  if (entity.vip === true || entity.isVIP === true) return true;
  if (entity.estado) {
    const estadoUpper = typeof entity.estado === 'string' ? entity.estado.toUpperCase() : '';
    if (estadoUpper === 'VIP' || estadoUpper.includes('VIP')) return true;
    if (Array.isArray(entity.estado)) return entity.estado.map(e => String(e).toUpperCase()).includes('VIP');
  }
  return false;
};

export const isEntityDestacada = (entity) => {
  if (!entity) return false;
  if (entity.destacada === true || entity.isDestacada === true) return true;
  if (entity.estado) {
    const estadoStr = typeof entity.estado === 'string' ? entity.estado : '';
    if (estadoStr === 'Destacada' || estadoStr.includes('Destacada')) return true;
    if (Array.isArray(entity.estado)) return entity.estado.includes('Destacada');
  }
  return false;
};

export const getIndependienteZonasTrabajo = (independiente) => {
  if (!independiente || !independiente.zonas) return [];
  try {
    if (typeof independiente.zonas === 'string') {
      // Attempt to parse if it looks like a JSON array string
      if (independiente.zonas.startsWith('[') && independiente.zonas.endsWith(']')) {
        return JSON.parse(independiente.zonas);
      }
      // If it's a comma-separated string, split it (optional, depends on data format)
      // For now, assume it's either a valid JSON array string or an actual array
      return [independiente.zonas]; // Fallback for single string zone
    }
    if (Array.isArray(independiente.zonas)) {
      return independiente.zonas;
    }
  } catch (error) {
    logger.error('Error al parsear zonas de trabajo:', error, independiente.zonas);
  }
  return [];
};
