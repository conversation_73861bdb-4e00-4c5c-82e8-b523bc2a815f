import logger from '../services/logService';

/**
 * Función para sanitizar entradas de texto
 * Previene inyección de código y XSS
 */
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/`/g, '&#96;');
};

/**
 * Función para validar IDs
 * Asegura que el ID tenga un formato válido
 */
export const validateId = (id) => {
  // Verificar que el ID sea una cadena y tenga el formato esperado
  if (!id || typeof id !== 'string' || !/^[a-zA-Z0-9_-]+$/.test(id)) {
    throw new Error('ID inválido');
  }
  return id;
};

/**
 * Función centralizada para manejar errores de API
 */
export const handleApiError = (error, defaultMessage) => {
  // Registrar el error para depuración
  logger.error(defaultMessage, error);

  // Crear un objeto de error con información limitada para el usuario
  const userError = {
    message: defaultMessage,
    status: error.status || 500,
    isHandled: true
  };

  // Si estamos en desarrollo, agregar más detalles
  if (process.env.NODE_ENV === 'development') {
    userError.details = error.message;
  }

  return userError;
};
