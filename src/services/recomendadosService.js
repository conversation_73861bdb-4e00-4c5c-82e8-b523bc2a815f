import { modelosService } from './pocketbase';
import { independientesService } from './independientesService';
import logger from './logService';

/**
 * Servicio para gestionar las recomendaciones de modelos
 */
const recomendadosService = {
  /**
   * Obtiene modelos recomendados aleatoriamente
   * @param {string} currentModelId - ID del modelo actual para excluirlo de las recomendaciones
   * @param {string} modelType - Tipo de modelo ('brujita' o 'independiente')
   * @param {number} limit - Número máximo de recomendaciones a devolver
   * @param {Array<string>} excludeTypes - Tipos de modelos a excluir ('brujita' o 'independiente')
   * @returns {Promise<Array>} - Array de modelos recomendados
   */
  getRecomendados: async (currentModelId, modelType, limit = 3, excludeTypes = []) => {
    try {
      // Paso 1: Obtener todos los modelos disponibles (brujitas e independientes)
      let allModels = [];
      let brujitasWithType = [];
      let independientesWithType = [];

      // Obtener brujitas
      const brujitasResponse = await modelosService.getAll();
      if (brujitasResponse && brujitasResponse.items) {
        brujitasWithType = brujitasResponse.items.map(item => ({
          ...item,
          modelType: 'brujita'
        }));
        // Solo añadir si no están excluidas
        if (!excludeTypes.includes('brujita')) {
          allModels = [...allModels, ...brujitasWithType];
        }
      }

      // Obtener independientes
      const independientesResponse = await independientesService.getAll();
      if (independientesResponse && independientesResponse.items) {
        independientesWithType = independientesResponse.items.map(item => ({
          ...item,
          modelType: 'independiente'
        }));
        // Solo añadir si no están excluidas
        if (!excludeTypes.includes('independiente')) {
          allModels = [...allModels, ...independientesWithType];
        }
      }

      // Registrar información detallada para depuración
      logger.debug(`Total de modelos obtenidos: ${allModels.length}`);
      logger.debug(`Brujitas: ${brujitasWithType.length}, Independientes: ${independientesWithType.length}`);

      // Paso 2: Filtrar modelos no disponibles y el modelo actual
      let filteredModels = allModels.filter(model => {
        // Excluir el modelo actual
        if (model.id === currentModelId) return false;

        // Verificar disponibilidad en diferentes formatos posibles
        let isDisponible = true; // Por defecto asumimos que está disponible

        // Si tiene un campo 'disponible' explícito como booleano
        if (model.disponible === false) isDisponible = false;

        // Si tiene un campo 'disponibilidad' como string
        if (model.disponibilidad === 'No disponible') isDisponible = false;

        // Si tiene un campo 'estado' que indica no disponibilidad
        if (model.estado === 'No disponible') isDisponible = false;

        // Si no está disponible, excluir el modelo
        if (!isDisponible) return false;

        return true;
      });

      // Registrar cuántos modelos quedaron después del filtrado
      logger.debug(`Modelos disponibles después del filtrado: ${filteredModels.length}`);

      // Paso 3: Separar modelos por tipo para poder priorizarlos
      let modelosPrincipales = filteredModels.filter(model => model.modelType === modelType);
      let modelosComplementarios = filteredModels.filter(model => model.modelType !== modelType);
      
      logger.debug(`Modelos principales (${modelType}): ${modelosPrincipales.length}`);
      logger.debug(`Modelos complementarios: ${modelosComplementarios.length}`);
      
      // Paso 4: Mezclar aleatoriamente cada grupo por separado
      const modelosPrincipalesMezclados = shuffleArray(modelosPrincipales);
      const modelosComplementariosMezclados = shuffleArray(modelosComplementarios);
      
      // Paso 5: Intentar obtener exactamente 'limit' recomendaciones
      let result = [];
      
      // Primero añadir modelos principales (del mismo tipo)
      result = [...modelosPrincipalesMezclados.slice(0, limit)];
      
      // Si no tenemos suficientes modelos principales, complementar con el otro tipo
      if (result.length < limit) {
        const faltantes = limit - result.length;
        logger.debug(`Faltan ${faltantes} modelos para completar las ${limit} recomendaciones solicitadas`);
        
        // Añadir modelos complementarios hasta alcanzar el límite
        const complementarios = modelosComplementariosMezclados.slice(0, faltantes);
        result = [...result, ...complementarios];
        
        logger.debug(`Se añadieron ${complementarios.length} modelos complementarios`);
      }
      
      // Paso 6: Si aún no tenemos suficientes, intentar incluir modelos excluidos
      if (result.length < limit && excludeTypes.length > 0) {
        logger.debug(`Aún faltan modelos. Intentando incluir tipos excluidos...`);
        
        // Crear un array con todos los modelos disponibles, incluyendo los tipos excluidos
        let todosLosModelos = [];
        
        // Si 'brujita' estaba excluido pero necesitamos más modelos
        if (excludeTypes.includes('brujita')) {
          const brujitasDisponibles = brujitasWithType.filter(model => {
            if (model.id === currentModelId) return false;
            let isDisponible = true;
            if (model.disponible === false) isDisponible = false;
            if (model.disponibilidad === 'No disponible') isDisponible = false;
            if (model.estado === 'No disponible') isDisponible = false;
            return isDisponible;
          });
          todosLosModelos = [...todosLosModelos, ...shuffleArray(brujitasDisponibles)];
        }
        
        // Si 'independiente' estaba excluido pero necesitamos más modelos
        if (excludeTypes.includes('independiente')) {
          const independientesDisponibles = independientesWithType.filter(model => {
            if (model.id === currentModelId) return false;
            let isDisponible = true;
            if (model.disponible === false) isDisponible = false;
            if (model.disponibilidad === 'No disponible') isDisponible = false;
            if (model.estado === 'No disponible') isDisponible = false;
            return isDisponible;
          });
          todosLosModelos = [...todosLosModelos, ...shuffleArray(independientesDisponibles)];
        }
        
        // Filtrar los que ya están en el resultado
        const idsActuales = result.map(model => model.id);
        const modelosAdicionales = todosLosModelos.filter(model => !idsActuales.includes(model.id));
        
        // Añadir los modelos adicionales necesarios
        const faltantes = limit - result.length;
        const complementariosAdicionales = modelosAdicionales.slice(0, faltantes);
        result = [...result, ...complementariosAdicionales];
        
        logger.debug(`Se añadieron ${complementariosAdicionales.length} modelos de tipos previamente excluidos`);
      }
      
      // Verificar si estamos devolviendo menos recomendaciones de las solicitadas
      if (result.length < limit) {
        logger.debug(`⚠️ Advertencia: Se solicitaron ${limit} recomendaciones pero solo se pudieron obtener ${result.length}`);
        logger.debug(`No hay suficientes modelos disponibles en la base de datos para mostrar ${limit} recomendaciones`);
      } else {
        logger.debug(`✅ Éxito: Se obtuvieron exactamente ${result.length} recomendaciones como se solicitó`);
      }

      // Registrar cuántos modelos se devolverán finalmente
      logger.debug(`Devolviendo ${result.length} recomendaciones (${result.filter(m => m.modelType === modelType).length} principales y ${result.filter(m => m.modelType !== modelType).length} complementarias)`);

      return result;
    } catch (error) {
      logger.error('Error al obtener recomendados:', error);
      return [];
    }
  }
};

/**
 * Mezcla aleatoriamente un array (algoritmo Fisher-Yates)
 * @param {Array} array - Array a mezclar
 * @returns {Array} - Array mezclado
 */
function shuffleArray(array) {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

export default recomendadosService;
