import { db } from './pocketbase';
import { API_URL, SECURITY_CONFIG } from '../config';
import logger from './logService';

// Funciones de utilidad
const handleApiError = (error, defaultMessage = 'Error en la API') => {
  // Registrar el error para depuración
  logger.error(`${defaultMessage}:`, error);

  // Determinar el mensaje de error para el usuario
  let userMessage = defaultMessage;

  if (error.response) {
    // Si hay una respuesta del servidor con un mensaje de error
    userMessage = error.response.message || defaultMessage;
  } else if (error.message) {
    // Si es un error de JavaScript con un mensaje
    userMessage = error.message;
  }

  // Devolver un objeto de error con información útil
  return {
    message: userMessage,
    originalError: error,
    timestamp: new Date().toISOString()
  };
};

// Validar ID para prevenir inyecciones
const validateId = (id) => {
  // Verificar que el ID tenga un formato válido (alfanumérico)
  if (!/^[a-zA-Z0-9]+$/.test(id)) {
    throw new Error('ID inválido');
  }
  return id;
};

// Sanitizar entrada para prevenir XSS
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;

  // Reemplazar caracteres peligrosos
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};

/**
 * Servicio para gestionar las independientes en PocketBase
 */
export const independientesService = {
  // Obtener URL de imagen
  getImageUrl: (record, fileName) => {
    if (!record || !fileName) return '/img/placeholder.png';

    try {
      // Validar que el record tenga un ID válido
      if (!record.id || typeof record.id !== 'string') {
        throw new Error('ID de registro inválido');
      }

      // Validar que el nombre de archivo sea seguro
      if (typeof fileName !== 'string' || fileName.includes('../') || fileName.includes('..\\')) {
        throw new Error('Nombre de archivo inválido');
      }

      // Construir la URL manualmente siguiendo el formato de PocketBase
      const url = `${API_URL}/api/files/independientes/${record.id}/${encodeURIComponent(fileName)}`;
      return url;
    } catch (error) {
      logger.error('Error al generar URL de imagen:', error);
      return '/img/placeholder.png';
    }
  },

  // Caché para almacenar resultados de consultas recientes
  _cache: {
    data: null,
    timestamp: 0,
    loading: false,
    pendingRequests: []
  },

  // Tiempo de expiración de la caché en milisegundos (30 segundos)
  _cacheExpiry: 30000,

  // Obtener todas las independientes
  getAll: async () => {
    // Verificar si hay una solicitud en curso
    if (independientesService._cache.loading) {
      // Si hay una solicitud en curso, devolver una promesa que se resolverá cuando termine
      return new Promise((resolve) => {
        independientesService._cache.pendingRequests.push(resolve);
      });
    }

    // Verificar si hay datos en caché y si son válidos
    const now = Date.now();
    if (independientesService._cache.data && (now - independientesService._cache.timestamp < independientesService._cacheExpiry)) {
      return independientesService._cache.data;
    }

    try {
      // Marcar como cargando para evitar solicitudes duplicadas
      independientesService._cache.loading = true;

      // Usar fetch con la URL centralizada
      const response = await fetch(`${API_URL}/api/collections/independientes/records`);

      if (!response.ok) {
        throw new Error(`Error HTTP: ${response.status}`);
      }

      const data = await response.json();

      // Verificar explícitamente si data.items existe
      if (!data || !data.items) {
        logger.warn('La respuesta de independientes no contiene la propiedad items o es nula');
        const result = { items: [], totalItems: 0, totalPages: 0 };
        
        // Actualizar caché
        independientesService._cache.data = result;
        independientesService._cache.timestamp = now;
        
        return result;
      }

      // Preparar resultado
      const result = { 
        items: data.items, 
        totalItems: data.totalItems || 0,
        totalPages: data.totalPages || 0
      };

      // Actualizar caché
      independientesService._cache.data = result;
      independientesService._cache.timestamp = now;

      // Resolver todas las promesas pendientes
      if (independientesService._cache.pendingRequests.length > 0) {
        independientesService._cache.pendingRequests.forEach(resolve => resolve(result));
        independientesService._cache.pendingRequests = [];
      }

      return result;
    } catch (error) {
      const handledError = handleApiError(error, 'Error al obtener independientes');
      // Devolver un array vacío para no romper la UI
      const result = { items: [], totalItems: 0, totalPages: 0, error: handledError };
      
      // Resolver todas las promesas pendientes con el error
      if (independientesService._cache.pendingRequests.length > 0) {
        independientesService._cache.pendingRequests.forEach(resolve => resolve(result));
        independientesService._cache.pendingRequests = [];
      }
      
      return result;
    } finally {
      // Marcar como no cargando
      independientesService._cache.loading = false;
    }
  },

  // Obtener una independiente por ID
  getById: async (id) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Usar PocketBase para obtener el registro
      const record = await db.collection('independientes').getOne(validatedId, {
        expand: 'relaciones,categorias'
      });

      return record;
    } catch (error) {
      const handledError = handleApiError(error, `Error al obtener independiente con ID ${id}`);
      throw handledError;
    }
  },

  // Crear una nueva independiente
  create: async (independienteData) => {
    try {
      // Crear una copia de los datos para manipularlos
      const formData = new FormData();

      // Lista de campos de texto que deben ser sanitizados
      const textFields = ['nombre', 'biografia', 'perfil_corporal'];

      // Copiar todos los campos del FormData original
      for (let [key, value] of independienteData.entries()) {
        // Manejar el campo disponible de manera especial
        if (key === 'disponible') {
          // Convertir a un valor booleano válido para PocketBase
          if (value === true || value === 'true' || value === '1' || value === 1) {
            formData.append('disponible', true);
          } else {
            formData.append('disponible', false);
          }
        } else if (textFields.includes(key) && typeof value === 'string') {
          // Sanitizar campos de texto para prevenir XSS
          formData.append(key, sanitizeInput(value));
        } else {
          // Para otros campos (incluyendo archivos), copiar tal cual
          formData.append(key, value);
        }
      }

      // Asegurarse de que disponible siempre tenga un valor
      if (!formData.has('disponible')) {
        formData.append('disponible', true); // Valor por defecto
      }

      // Validación básica de campos requeridos
      if (!formData.get('nombre')) {
        throw new Error('El nombre es obligatorio');
      }

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        const result = await db.collection('independientes').create(formData, options);
        
        // Invalidar la caché para forzar una recarga en la próxima solicitud
        if (independientesService._cache.data) {
          independientesService._cache.timestamp = 0;
        }
        
        return result;
      }

      const result = await db.collection('independientes').create(formData);
      
      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (independientesService._cache.data) {
        independientesService._cache.timestamp = 0;
      }
      
      return result;
    } catch (error) {
      const handledError = handleApiError(error, 'Error al crear independiente');
      throw handledError;
    }
  },

  // Actualizar una independiente existente
  update: async (id, independienteData) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Crear una copia de los datos para manipularlos
      const formData = new FormData();

      // Lista de campos de texto que deben ser sanitizados
      const textFields = ['nombre', 'biografia', 'perfil_corporal'];

      // Copiar todos los campos del FormData original
      for (let [key, value] of independienteData.entries()) {
        // Manejar el campo disponible de manera especial
        if (key === 'disponible') {
          // Convertir a un valor booleano válido para PocketBase
          if (value === true || value === 'true' || value === '1' || value === 1) {
            formData.append('disponible', true);
          } else {
            formData.append('disponible', false);
          }
        } else if (textFields.includes(key) && typeof value === 'string') {
          // Sanitizar campos de texto para prevenir XSS
          formData.append(key, sanitizeInput(value));
        } else {
          // Para otros campos (incluyendo archivos), copiar tal cual
          formData.append(key, value);
        }
      }

      // Asegurarse de que disponible siempre tenga un valor
      if (!formData.has('disponible')) {
        formData.append('disponible', true); // Valor por defecto
      }

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        const result = await db.collection('independientes').update(validatedId, formData, options);
        
        // Invalidar la caché para forzar una recarga en la próxima solicitud
        if (independientesService._cache.data) {
          independientesService._cache.timestamp = 0;
        }
        
        return result;
      }

      const result = await db.collection('independientes').update(validatedId, formData);
      
      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (independientesService._cache.data) {
        independientesService._cache.timestamp = 0;
      }
      
      return result;
    } catch (error) {
      const handledError = handleApiError(error, `Error al actualizar independiente con ID ${id}`);
      throw handledError;
    }
  },

  // Eliminar una independiente
  delete: async (id) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        const result = await db.collection('independientes').delete(validatedId, options);
        
        // Invalidar la caché para forzar una recarga en la próxima solicitud
        if (independientesService._cache.data) {
          independientesService._cache.timestamp = 0;
        }
        
        return result;
      }

      const result = await db.collection('independientes').delete(validatedId);
      
      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (independientesService._cache.data) {
        independientesService._cache.timestamp = 0;
      }
      
      return result;
    } catch (error) {
      const handledError = handleApiError(error, `Error al eliminar independiente con ID ${id}`);
      throw handledError;
    }
  },
};
