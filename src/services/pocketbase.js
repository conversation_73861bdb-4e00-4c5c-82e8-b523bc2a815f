import PocketBase from 'pocketbase';
import { API_URL, SECURITY_CONFIG } from '../config';
import logger from './logService';

// Inicializa la conexión con PocketBase
const db = new PocketBase(API_URL);

// Configurar opciones globales para evitar problemas de auto-cancelación
db.autoCancellation(false);

// Tiempo de expiración de la sesión en milisegundos (desde configuración)
const SESSION_EXPIRY = SECURITY_CONFIG.SESSION_EXPIRY;

// Almacenar el tiempo de la última actividad
let lastActivity = Date.now();

/**
 * Función para sanitizar entradas de texto
 * Previene inyección de código y XSS
 */
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/`/g, '&#96;');
};

/**
 * Función para validar IDs
 * Asegura que el ID tenga un formato válido
 */
const validateId = (id) => {
  // Verificar que el ID sea una cadena y tenga el formato esperado
  if (!id || typeof id !== 'string' || !/^[a-zA-Z0-9_-]+$/.test(id)) {
    throw new Error('ID inválido');
  }
  return id;
};

/**
 * Función centralizada para manejar errores de API
 */
const handleApiError = (error, defaultMessage) => {
  // Registrar el error para depuración
  logger.error(defaultMessage, error);

  // Crear un objeto de error con información limitada para el usuario
  const userError = {
    message: defaultMessage,
    status: error.status || 500,
    isHandled: true
  };

  // Si estamos en desarrollo, agregar más detalles
  if (process.env.NODE_ENV === 'development') {
    userError.details = error.message;
  }

  return userError;
};

// Caché para almacenar resultados de consultas recientes
const cache = {
  modelos: {
    data: null,
    timestamp: 0,
    loading: false,
    pendingRequests: []
  }
};

// Tiempo de expiración de la caché en milisegundos (30 segundos)
const CACHE_EXPIRY = 30000;

export const modelosService = {
  // Obtener todos los modelos
  getAll: async () => {
    // Verificar si hay una solicitud en curso
    if (cache.modelos.loading) {
      // Si hay una solicitud en curso, devolver una promesa que se resolverá cuando termine
      return new Promise((resolve) => {
        cache.modelos.pendingRequests.push(resolve);
      });
    }

    // Verificar si hay datos en caché y si son válidos
    const now = Date.now();
    if (cache.modelos.data && (now - cache.modelos.timestamp < CACHE_EXPIRY)) {
      return cache.modelos.data;
    }

    try {
      // Marcar como cargando para evitar solicitudes duplicadas
      cache.modelos.loading = true;

      // Usar fetch con la URL centralizada
      const response = await fetch(`${API_URL}/api/collections/brujitas/records`);

      if (!response.ok) {
        throw new Error(`Error HTTP: ${response.status}`);
      }

      const data = await response.json();

      // Verificar explícitamente si data.items existe
      if (!data || !data.items) {
        logger.warn('La respuesta de modelos no contiene la propiedad items o es nula');
        const result = { items: [], totalItems: 0, totalPages: 0 };
        
        // Actualizar caché
        cache.modelos.data = result;
        cache.modelos.timestamp = now;
        
        return result;
      }

      // Preparar resultado
      const result = { 
        items: data.items, 
        totalItems: data.totalItems || 0,
        totalPages: data.totalPages || 0
      };

      // Actualizar caché
      cache.modelos.data = result;
      cache.modelos.timestamp = now;

      // Resolver todas las promesas pendientes
      if (cache.modelos.pendingRequests.length > 0) {
        cache.modelos.pendingRequests.forEach(resolve => resolve(result));
        cache.modelos.pendingRequests = [];
      }

      return result;
    } catch (error) {
      const handledError = handleApiError(error, 'Error al obtener modelos');
      // Devolver un array vacío para no romper la UI
      const result = { items: [], totalItems: 0, totalPages: 0, error: handledError };
      
      // Resolver todas las promesas pendientes con el error
      if (cache.modelos.pendingRequests.length > 0) {
        cache.modelos.pendingRequests.forEach(resolve => resolve(result));
        cache.modelos.pendingRequests = [];
      }
      
      return result;
    } finally {
      // Marcar como no cargando
      cache.modelos.loading = false;
    }
  },

  // Obtener un modelo por ID
  getById: async (id) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Usar una función personalizada que no muestra errores en consola
      const silentFetch = async (url) => {
        return new Promise((resolve) => {
          // Crear un objeto que interceptará la petición
          const originalFetch = window.fetch;

          // Reemplazar temporalmente la función fetch para esta petición
          window.fetch = async function(input, init) {
            // Restaurar la función original inmediatamente
            window.fetch = originalFetch;

            try {
              // Realizar la petición con la función original
              const response = await originalFetch(input, init);

              // Clonar la respuesta para poder leerla múltiples veces
              const clonedResponse = response.clone();

              // Si es un 404, devolver un objeto de error estructurado
              if (response.status === 404) {
                resolve({
                  error: {
                    status: 404,
                    message: `La brujita con ID ${id} no existe o ha sido eliminada.`,
                    isHandled: true
                  }
                });
                return;
              }

              // Si no es un 200, devolver un objeto de error estructurado
              if (!response.ok) {
                resolve({
                  error: {
                    status: response.status,
                    message: `Error HTTP: ${response.status}`,
                    isHandled: true
                  }
                });
                return;
              }

              // Si todo va bien, devolver los datos
              const data = await clonedResponse.json();
              resolve(data);
            } catch (err) {
              // Si hay un error, devolver un objeto de error estructurado
              resolve({
                error: {
                  status: 500,
                  message: `Error al procesar la petición para la brujita con ID ${id}.`,
                  isHandled: true
                }
              });
            }
          };

          // Iniciar la petición
          fetch(url).catch(() => {
            // Restaurar la función original si hay un error
            window.fetch = originalFetch;

            // Devolver un objeto de error estructurado
            resolve({
              error: {
                status: 500,
                message: `Error al procesar la petición para la brujita con ID ${id}.`,
                isHandled: true
              }
            });
          });
        });
      };

      // Realizar la petición silenciosa
      const data = await silentFetch(`${API_URL}/api/collections/brujitas/records/${validatedId}`);

      // Verificar si hay un error en la respuesta
      if (data && data.error) {
        return data;
      }

      // Verificar si los datos recibidos son válidos
      if (!data || !data.id || !data.collectionId || !data.collectionName) {
        return {
          error: {
            status: 404,
            message: `No se encontró información válida para la brujita con ID ${id}.`,
            isHandled: true
          }
        };
      }

      return data;
    } catch (error) {
      // Este catch no debería ejecutarse nunca, pero lo mantenemos por seguridad
      return {
        error: {
          status: 500,
          message: `Error al obtener la brujita con ID ${id}.`,
          isHandled: true
        }
      };
    }
  },

  // Crear un nuevo modelo
  create: async (dataObject) => {
    try {
      // Sanitizar campos de texto si es necesario (asumiendo que dataObject es un objeto plano)
      const textFields = ['nombre', 'biografia', 'perfil_corporal'];
      const sanitizedData = { ...dataObject };
      textFields.forEach(field => {
        if (sanitizedData[field] && typeof sanitizedData[field] === 'string') {
          sanitizedData[field] = sanitizeInput(sanitizedData[field]);
        }
      });

      // Validación básica de campos requeridos
      if (!sanitizedData.nombre) {
        throw new Error('El nombre es obligatorio');
      }
      
      // Asegurarse de que disponible siempre tenga un valor
      if (sanitizedData.disponible === undefined) {
        sanitizedData.disponible = true; // Valor por defecto
      }


      const csrfToken = sessionStorage.getItem('csrfToken');
      const options = csrfToken ? { headers: { 'X-CSRF-Token': csrfToken } } : {};
      
      const result = await db.collection('brujitas').create(sanitizedData, options);
      
      // Invalidar la caché
      if (cache.modelos.data) {
        cache.modelos.timestamp = 0;
      }
      
      return result;
    } catch (error) {
      const handledError = handleApiError(error, 'Error al crear modelo');
      throw handledError;
    }
  },

  // Actualizar un modelo existente
  update: async (id, dataObject) => {
    try {
      const validatedId = validateId(id);
      let payload;

      if (dataObject instanceof FormData) {
        payload = dataObject;
      } else {
        // Sanitizar campos de texto si es necesario para plain objects
        const textFields = ['nombre', 'biografia', 'perfil_corporal'];
        const sanitizedObject = { ...dataObject };
        textFields.forEach(field => {
          if (sanitizedObject[field] && typeof sanitizedObject[field] === 'string') {
            sanitizedObject[field] = sanitizeInput(sanitizedObject[field]);
          }
        });
        
        // Asegurarse de que disponible siempre tenga un valor si se está actualizando explícitamente
        if (sanitizedObject.hasOwnProperty('disponible') && sanitizedObject.disponible === undefined) {
           // Si se incluyó 'disponible' pero es undefined, se puede establecer un valor por defecto o manejar como error
           // Por ahora, si se envía explícitamente como undefined, PocketBase podría interpretarlo como no cambiar o error.
           // Si se quiere asegurar un valor, se puede hacer:
           // sanitizedObject.disponible = sanitizedObject.disponible !== undefined ? sanitizedObject.disponible : true;
           // O dejar que PocketBase maneje el undefined si el campo no es obligatorio.
        }
        payload = sanitizedObject;
      }

      const csrfToken = sessionStorage.getItem('csrfToken');
      const options = csrfToken ? { headers: { 'X-CSRF-Token': csrfToken } } : {};

      const result = await db.collection('brujitas').update(validatedId, payload, options);
      
      // Invalidar la caché
      if (cache.modelos.data) {
        cache.modelos.timestamp = 0;
      }
      
      return result;
    } catch (error) {
      const handledError = handleApiError(error, `Error al actualizar modelo con ID ${id}`);
      throw handledError;
    }
  },

  // Eliminar un modelo
  delete: async (id) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        const result = await db.collection('brujitas').delete(validatedId, options);
        
        // Invalidar la caché para forzar una recarga en la próxima solicitud
        if (cache.modelos.data) {
          cache.modelos.timestamp = 0;
        }
        
        return result;
      }

      const result = await db.collection('brujitas').delete(validatedId);
      
      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (cache.modelos.data) {
        cache.modelos.timestamp = 0;
      }
      
      return result;
    } catch (error) {
      const handledError = handleApiError(error, `Error al eliminar modelo con ID ${id}`);
      throw handledError;
    }
  },

  // Obtener la URL de una imagen
  getImageUrl: (record, fileName) => {
    // Usar la nueva API de PocketBase para obtener URLs de archivos
    if (!record || !fileName) return '/img/placeholder.png';

    try {
      // Validar que el record tenga un ID válido
      if (!record.id || typeof record.id !== 'string') {
        throw new Error('ID de registro inválido');
      }

      // Validar que el nombre de archivo sea seguro
      if (typeof fileName !== 'string' || fileName.includes('../') || fileName.includes('..\\')) {
        throw new Error('Nombre de archivo inválido');
      }

      // Construir la URL manualmente siguiendo el formato de PocketBase
      const url = `${API_URL}/api/files/brujitas/${record.id}/${encodeURIComponent(fileName)}`;
      return url;
    } catch (error) {
      logger.error('Error al generar URL de imagen:', error);
      return '/img/placeholder.png';
    }
  }
};

/**
 * Genera un token CSRF aleatorio
 */
const generateCsrfToken = () => {
  const array = new Uint8Array(16);
  window.crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Actualiza el tiempo de última actividad
 */
const updateLastActivity = () => {
  lastActivity = Date.now();
};

/**
 * Verifica si la sesión ha expirado
 */
const isSessionExpired = () => {
  return Date.now() - lastActivity > SESSION_EXPIRY;
};

// Servicio de autenticación
export const authService = {
  // Iniciar sesión
  login: async (email, password) => {
    try {
      // Validar entradas
      if (!email || typeof email !== 'string' || !email.includes('@')) {
        throw new Error('Email inválido');
      }

      if (!password || typeof password !== 'string' || password.length < 6) {
        throw new Error('Contraseña inválida');
      }

      // Sanitizar email
      const sanitizedEmail = sanitizeInput(email);

      // Autenticar usando email/contraseña
      const authData = await db.collection('users').authWithPassword(sanitizedEmail, password);

      // Generar y almacenar token CSRF
      const csrfToken = generateCsrfToken();
      sessionStorage.setItem('csrfToken', csrfToken);

      // Actualizar tiempo de actividad
      updateLastActivity();

      // Registrar inicio de sesión exitoso (sin datos sensibles)
      logger.info('Usuario autenticado correctamente');

      return authData;
    } catch (error) {
      const handledError = handleApiError(error, 'Error de inicio de sesión');
      throw handledError;
    }
  },

  // Cerrar sesión
  logout: () => {
    try {
      // Limpiar autenticación
      db.authStore.clear();

      // Eliminar token CSRF
      sessionStorage.removeItem('csrfToken');

      logger.info('Sesión cerrada correctamente');
    } catch (error) {
      logger.error('Error al cerrar sesión:', error);
    }
  },

  // Verificar si el usuario está autenticado
  isAuthenticated: () => {
    // Verificar si la sesión ha expirado por inactividad
    if (isSessionExpired()) {
      // Si ha expirado, cerrar sesión automáticamente
      authService.logout();
      return false;
    }

    // Actualizar tiempo de actividad
    updateLastActivity();

    return db.authStore.isValid;
  },

  // Obtener el token de autenticación
  getToken: () => {
    // Verificar si la sesión ha expirado antes de devolver el token
    if (isSessionExpired()) {
      authService.logout();
      return null;
    }

    // Actualizar tiempo de actividad
    updateLastActivity();

    return db.authStore.token;
  },

  // Obtener el modelo de usuario autenticado
  getUser: () => {
    // Verificar si la sesión ha expirado antes de devolver el usuario
    if (isSessionExpired()) {
      authService.logout();
      return null;
    }

    // Actualizar tiempo de actividad
    updateLastActivity();

    return db.authStore.model;
  },

  // Verificar si el usuario tiene permisos de administrador
  isAdmin: () => {
    const user = authService.getUser();
    // Verificar si el usuario existe y tiene el rol de administrador
    return user && user.role === 'admin';
  }
};

// Configurar un intervalo para verificar la expiración de la sesión cada minuto
setInterval(() => {
  if (authService.isAuthenticated() && isSessionExpired()) {
    logger.info('Sesión expirada por inactividad');
    authService.logout();
    // Redirigir al usuario a la página de inicio si es necesario
    if (window.location.pathname.startsWith('/admin')) {
      window.location.href = '/';
    }
  }
}, 60000); // Verificar cada minuto

// Exportar db también si se necesita acceso directo en otros lugares
export { db };
