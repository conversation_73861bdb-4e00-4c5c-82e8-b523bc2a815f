import { db } from './pocketbase';
import { API_URL, SECURITY_CONFIG } from '../config';
import logger from './logService';
import { sanitizeInput, validateId, handleApiError } from '../utils/helpers';

/**
 * Servicio para gestionar los banners en PocketBase
 */
export const bannerService = {
  // Caché para almacenar resultados de consultas recientes
  _cache: {
    data: null,
    timestamp: 0,
    loading: false,
    pendingRequests: []
  },

  // Tiempo de expiración de la caché en milisegundos (30 segundos)
  _cacheExpiry: 30000,

  // Obtener todos los banners
  getAll: async () => {
    // Verificar si hay una solicitud en curso
    if (bannerService._cache.loading) {
      // Si hay una solicitud en curso, devolver una promesa que se resolverá cuando termine
      return new Promise((resolve) => {
        bannerService._cache.pendingRequests.push(resolve);
      });
    }

    // Verificar si hay datos en caché y si son válidos
    const now = Date.now();
    if (bannerService._cache.data && (now - bannerService._cache.timestamp < bannerService._cacheExpiry)) {
      return bannerService._cache.data;
    }

    try {
      // Marcar como cargando para evitar solicitudes duplicadas
      bannerService._cache.loading = true;

      // Usar fetch con la URL centralizada
      const response = await fetch(`${API_URL}/api/collections/banners/records?sort=orden`);

      if (!response.ok) {
        throw new Error(`Error HTTP: ${response.status}`);
      }

      const data = await response.json();

      // Verificar explícitamente si data.items existe
      if (!data || !data.items) {
        logger.warn('La respuesta de banners no contiene la propiedad items o es nula');
        const result = { items: [], totalItems: 0, totalPages: 0 };
        
        // Actualizar caché
        bannerService._cache.data = result;
        bannerService._cache.timestamp = now;
        
        return result;
      }

      // Preparar resultado
      const result = { 
        items: data.items, 
        totalItems: data.totalItems || 0,
        totalPages: data.totalPages || 0
      };

      // Actualizar caché
      bannerService._cache.data = result;
      bannerService._cache.timestamp = now;

      // Resolver todas las promesas pendientes
      if (bannerService._cache.pendingRequests.length > 0) {
        bannerService._cache.pendingRequests.forEach(resolve => resolve(result));
        bannerService._cache.pendingRequests = [];
      }

      return result;
    } catch (error) {
      const handledError = handleApiError(error, 'Error al obtener banners');
      // Devolver un array vacío para no romper la UI
      const result = { items: [], totalItems: 0, totalPages: 0, error: handledError };
      
      // Resolver todas las promesas pendientes con el error
      if (bannerService._cache.pendingRequests.length > 0) {
        bannerService._cache.pendingRequests.forEach(resolve => resolve(result));
        bannerService._cache.pendingRequests = [];
      }
      
      return result;
    } finally {
      // Marcar como no cargando
      bannerService._cache.loading = false;
    }
  },

  // Obtener un banner por ID
  getById: async (id) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      const record = await db.collection('banners').getOne(validatedId);
      return record;
    } catch (error) {
      const handledError = handleApiError(error, `Error al obtener banner con ID ${id}`);
      throw handledError;
    }
  },

  // Crear un nuevo banner
  create: async (bannerData) => {
    try {
      // Crear una copia de los datos para manipularlos
      const formData = new FormData();

      // Lista de campos de texto que deben ser sanitizados
      const textFields = ['titulo', 'descripcion', 'texto_resaltado', 'whatsapp_texto', 'telegram_texto', 'whatsapp_enlace', 'telegram_enlace'];

      // Copiar todos los campos del FormData original
      for (let [key, value] of bannerData.entries()) {
        // Manejar el campo activo de manera especial
        if (key === 'activo') {
          // Convertir a un valor booleano válido para PocketBase
          if (value === true || value === 'true' || value === '1' || value === 1) {
            formData.append('activo', true);
          } else {
            formData.append('activo', false);
          }
        } else if (textFields.includes(key) && typeof value === 'string') {
          // Sanitizar campos de texto para prevenir XSS
          formData.append(key, sanitizeInput(value));
        } else if (key !== 'orden') { // No copiar el campo orden, lo calcularemos automáticamente
          // Para otros campos (incluyendo archivos), copiar tal cual
          formData.append(key, value);
        }
      }

      // Validación básica de campos requeridos
      if (!formData.get('titulo')) {
        throw new Error('El título es obligatorio');
      }

      // Por defecto, el banner estará activo
      if (!formData.has('activo')) {
        formData.append('activo', true);
      }

      // Obtener el siguiente orden disponible
      try {
        const response = await fetch(`${API_URL}/api/collections/banners/records?sort=-orden&limit=1`);
        if (response.ok) {
          const data = await response.json();
          const nextOrder = data.items.length > 0 ? data.items[0].orden + 1 : 1;
          formData.append('orden', nextOrder);
        } else {
          // Si hay un error, usar 1 como valor predeterminado
          formData.append('orden', 1);
        }
      } catch (err) {
        // Si hay un error, usar 1 como valor predeterminado
        formData.append('orden', 1);
        logger.error('Error al obtener el siguiente orden:', err);
      }

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        return await db.collection('banners').create(formData, options);
      }

      return await db.collection('banners').create(formData);
    } catch (error) {
      const handledError = handleApiError(error, 'Error al crear banner');
      throw handledError;
    }
  },

  // Actualizar un banner existente
  update: async (id, bannerData) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Crear una copia de los datos para manipularlos
      const formData = new FormData();

      // Lista de campos de texto que deben ser sanitizados
      const textFields = ['titulo', 'descripcion', 'texto_resaltado', 'whatsapp_texto', 'telegram_texto', 'whatsapp_enlace', 'telegram_enlace'];

      // Verificar si hay cambio de orden
      let newOrder = null;
      let oldOrder = null;

      // Obtener el banner actual para comparar el orden
      try {
        const currentBanner = await db.collection('banners').getOne(validatedId);
        oldOrder = currentBanner.orden;

        // Obtener el nuevo orden del formData
        for (let [key, value] of bannerData.entries()) {
          if (key === 'orden') {
            newOrder = parseInt(value, 10);
            break;
          }
        }
      } catch (err) {
        logger.error('Error al obtener banner actual:', err);
      }

      // Copiar todos los campos del FormData original
      for (let [key, value] of bannerData.entries()) {
        // Manejar el campo activo de manera especial
        if (key === 'activo') {
          // Convertir a un valor booleano válido para PocketBase
          if (value === true || value === 'true' || value === '1' || value === 1) {
            formData.append('activo', true);
          } else {
            formData.append('activo', false);
          }
        } else if (textFields.includes(key) && typeof value === 'string') {
          // Sanitizar campos de texto para prevenir XSS
          formData.append(key, sanitizeInput(value));
        } else {
          // Para otros campos (incluyendo archivos), copiar tal cual
          formData.append(key, value);
        }
      }

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      const options = csrfToken ? { headers: { 'X-CSRF-Token': csrfToken } } : {};

      // Actualizar el banner
      const updatedBanner = await db.collection('banners').update(validatedId, formData, options);
      
      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (bannerService._cache.data) {
        bannerService._cache.timestamp = 0;
      }

      // Si hay cambio de orden, reordenar los demás banners
      if (newOrder !== null && oldOrder !== null && newOrder !== oldOrder) {
        try {
          // Obtener todos los banners ordenados
          const response = await fetch(`${API_URL}/api/collections/banners/records?sort=orden`);
          if (response.ok) {
            const data = await response.json();
            const banners = data.items;

            // Reordenar banners
            for (const banner of banners) {
              if (banner.id === validatedId) continue; // Saltar el banner que acabamos de actualizar

              let newBannerOrder = banner.orden;

              // Si el banner se movió hacia abajo (orden mayor)
              if (newOrder > oldOrder) {
                if (banner.orden > oldOrder && banner.orden <= newOrder) {
                  newBannerOrder = banner.orden - 1;
                }
              }
              // Si el banner se movió hacia arriba (orden menor)
              else if (newOrder < oldOrder) {
                if (banner.orden >= newOrder && banner.orden < oldOrder) {
                  newBannerOrder = banner.orden + 1;
                }
              }

              // Si el orden cambió, actualizar el banner
              if (newBannerOrder !== banner.orden) {
                const orderFormData = new FormData();
                orderFormData.append('orden', newBannerOrder);
                await db.collection('banners').update(banner.id, orderFormData, options);
              }
            }
          }
        } catch (err) {
          logger.error('Error al reordenar banners:', err);
        }
      }

      return updatedBanner;
    } catch (error) {
      const handledError = handleApiError(error, `Error al actualizar banner con ID ${id}`);
      throw handledError;
    }
  },

  // Eliminar un banner
  delete: async (id) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Obtener el banner para saber su orden
      let orden = null;
      try {
        const banner = await db.collection('banners').getOne(validatedId);
        orden = banner.orden;
      } catch (err) {
        logger.error('Error al obtener banner para eliminar:', err);
      }

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      const options = csrfToken ? { headers: { 'X-CSRF-Token': csrfToken } } : {};

      // Eliminar el banner
      const result = await db.collection('banners').delete(validatedId, options);
      
      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (bannerService._cache.data) {
        bannerService._cache.timestamp = 0;
      }

      // Reordenar los banners restantes si conocemos el orden del eliminado
      if (orden !== null) {
        try {
          // Obtener todos los banners ordenados
          const response = await fetch(`${API_URL}/api/collections/banners/records?sort=orden`);
          if (response.ok) {
            const data = await response.json();
            const banners = data.items;

            // Reordenar banners
            for (const banner of banners) {
              // Si el banner tiene un orden mayor que el eliminado, decrementar
              if (banner.orden > orden) {
                const updateData = new FormData();
                updateData.append('orden', banner.orden - 1);
                await db.collection('banners').update(banner.id, updateData, options);
              }
            }
          }
        } catch (err) {
          logger.error('Error al reordenar banners después de eliminar:', err);
        }
      }

      return result;
    } catch (error) {
      const handledError = handleApiError(error, `Error al eliminar banner con ID ${id}`);
      throw handledError;
    }
  },

  // Obtener URL de imagen
  getImageUrl: (record, fileName) => {
    if (!record || !fileName) return '/img/placeholder.png';

    try {
      // Validar que el record tenga un ID válido
      if (!record.id || typeof record.id !== 'string') {
        throw new Error('ID de registro inválido');
      }

      // Validar que el nombre de archivo sea seguro
      if (typeof fileName !== 'string' || fileName.includes('../') || fileName.includes('..\\')) {
        throw new Error('Nombre de archivo inválido');
      }

      // Construir la URL manualmente siguiendo el formato de PocketBase
      const url = `${API_URL}/api/files/banners/${record.id}/${encodeURIComponent(fileName)}`;
      return url;
    } catch (error) {
      logger.error('Error al generar URL de imagen:', error);
      return '/img/placeholder.png';
    }
  },

  // Actualizar el orden de un banner
  updateOrder: async (id, newOrder) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Crear un objeto FormData para enviar el nuevo orden
      const formData = new FormData();
      formData.append('orden', newOrder);

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        return await db.collection('banners').update(validatedId, formData, options);
      }

      return await db.collection('banners').update(validatedId, formData);
    } catch (error) {
      const handledError = handleApiError(error, `Error al actualizar orden del banner con ID ${id}`);
      throw handledError;
    }
  }
};
