import { db } from './pocketbase';
import { API_URL } from '../config';
import logger from './logService';
import { sanitizeInput, validateId, handleApiError } from '../utils/helpers';

/**
 * Servicio para gestionar las ofertas en PocketBase
 */
export const ofertasService = {
  // Caché para almacenar resultados de consultas recientes
  _cache: {
    data: null,
    timestamp: 0,
    loading: false,
    pendingRequests: []
  },

  // Tiempo de expiración de la caché en milisegundos (30 segundos)
  _cacheExpiry: 30000,

  // Obtener todas las ofertas
  getAll: async (filter = {}) => {
    try {
      const now = Date.now();

      // Si hay datos en caché y no han expirado, usarlos
      if (
        ofertasService._cache.data &&
        now - ofertasService._cache.timestamp < ofertasService._cacheExpiry &&
        !ofertasService._cache.loading
      ) {
        logger.debug('Usando datos de ofertas en caché');
        return ofertasService._cache.data;
      }

      // Si ya hay una solicitud en curso, esperar a que termine
      if (ofertasService._cache.loading) {
        logger.debug('Esperando solicitud de ofertas en curso');
        return new Promise((resolve) => {
          ofertasService._cache.pendingRequests.push(resolve);
        });
      }

      // Marcar como cargando para evitar solicitudes duplicadas
      ofertasService._cache.loading = true;

      // Construir filtros para la consulta
      let queryParams = {};

      // Si se especifica visible, filtrar por ese valor
      if (filter.visible !== undefined) {
        queryParams.filter = `visible=${filter.visible}`;
      }

      // Realizar la consulta a PocketBase para obtener ofertas
      const ofertasRecords = await db.collection('ofertas').getList(1, 100, queryParams);

      // Obtener todas las brujitas para procesar sus días de ofertas
      const brujitasResponse = await db.collection('brujitas').getList(1, 200);
      const brujitas = brujitasResponse.items;

      // Procesar cada oferta para añadir las brujitas que tienen días seleccionados
      const ofertasConBrujitas = {
        ...ofertasRecords,
        items: ofertasRecords.items.map(oferta => {
          // Encontrar brujitas que tienen esta oferta en su campo ofertas_dias
          const brujitasConDias = brujitas.filter(brujita => {
            try {
              if (brujita.ofertas_dias) {
                let ofertasDias = {};

                // Parsear el campo ofertas_dias si es necesario
                if (typeof brujita.ofertas_dias === 'string') {
                  ofertasDias = JSON.parse(brujita.ofertas_dias);
                } else if (typeof brujita.ofertas_dias === 'object') {
                  ofertasDias = brujita.ofertas_dias;
                }

                // Verificar si hay días seleccionados para esta oferta
                return ofertasDias[oferta.id] && ofertasDias[oferta.id].length > 0;
              }
              return false;
            } catch (error) {
              logger.error(`Error al parsear ofertas_dias para brujita ${brujita.id}:`, error);
              return false;
            }
          }).map(brujita => {
            // Procesar los días seleccionados para esta oferta
            let diasSeleccionados = [];
            try {
              if (brujita.ofertas_dias) {
                let ofertasDias = {};

                // Parsear el campo ofertas_dias
                if (typeof brujita.ofertas_dias === 'string') {
                  ofertasDias = JSON.parse(brujita.ofertas_dias);
                } else if (typeof brujita.ofertas_dias === 'object') {
                  ofertasDias = brujita.ofertas_dias;
                }

                // Obtener los días para esta oferta específica
                diasSeleccionados = ofertasDias[oferta.id] || [];
              }
            } catch (error) {
              logger.error(`Error al obtener días para brujita ${brujita.id}:`, error);
            }

            // Devolver la brujita con sus días seleccionados
            return {
              ...brujita,
              diasSeleccionados
            };
          });

          // Devolver la oferta con sus brujitas asociadas
          return {
            ...oferta,
            brujitasConDias
          };
        })
      };

      // Actualizar caché
      ofertasService._cache.data = ofertasConBrujitas;
      ofertasService._cache.timestamp = now;
      ofertasService._cache.loading = false;

      // Resolver promesas pendientes
      if (ofertasService._cache.pendingRequests.length > 0) {
        ofertasService._cache.pendingRequests.forEach(resolve => resolve(ofertasConBrujitas));
        ofertasService._cache.pendingRequests = [];
      }

      return ofertasConBrujitas;
    } catch (error) {
      ofertasService._cache.loading = false;
      const handledError = handleApiError(error, 'Error al obtener ofertas');
      throw handledError;
    }
  },

  // Obtener una oferta por ID
  getById: async (id) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Usar PocketBase para obtener el registro
      const oferta = await db.collection('ofertas').getOne(validatedId);

      // Obtener todas las brujitas para procesar sus días de ofertas
      const brujitasResponse = await db.collection('brujitas').getList(1, 200);
      const brujitas = brujitasResponse.items;

      // Encontrar brujitas que tienen esta oferta en su campo ofertas_dias
      const brujitasConDias = brujitas.filter(brujita => {
        try {
          if (brujita.ofertas_dias) {
            let ofertasDias = {};

            // Parsear el campo ofertas_dias si es necesario
            if (typeof brujita.ofertas_dias === 'string') {
              ofertasDias = JSON.parse(brujita.ofertas_dias);
            } else if (typeof brujita.ofertas_dias === 'object') {
              ofertasDias = brujita.ofertas_dias;
            }

            // Verificar si hay días seleccionados para esta oferta
            return ofertasDias[oferta.id] && ofertasDias[oferta.id].length > 0;
          }
          return false;
        } catch (error) {
          logger.error(`Error al parsear ofertas_dias para brujita ${brujita.id}:`, error);
          return false;
        }
      }).map(brujita => {
        // Procesar los días seleccionados para esta oferta
        let diasSeleccionados = [];
        try {
          if (brujita.ofertas_dias) {
            let ofertasDias = {};

            // Parsear el campo ofertas_dias
            if (typeof brujita.ofertas_dias === 'string') {
              ofertasDias = JSON.parse(brujita.ofertas_dias);
            } else if (typeof brujita.ofertas_dias === 'object') {
              ofertasDias = brujita.ofertas_dias;
            }

            // Obtener los días para esta oferta específica
            diasSeleccionados = ofertasDias[oferta.id] || [];
          }
        } catch (error) {
          logger.error(`Error al obtener días para brujita ${brujita.id}:`, error);
        }

        // Devolver la brujita con sus días seleccionados
        return {
          ...brujita,
          diasSeleccionados
        };
      });

      // Devolver la oferta con sus brujitas asociadas
      return {
        ...oferta,
        brujitasConDias
      };
    } catch (error) {
      const handledError = handleApiError(error, `Error al obtener oferta con ID ${id}`);
      throw handledError;
    }
  },

  // Crear una nueva oferta
  create: async (ofertaData) => {
    try {
      // Crear una copia de los datos para manipularlos
      const formData = new FormData();

      // Lista de campos de texto que deben ser sanitizados
      const textFields = ['titulo', 'descripcion'];

      // Copiar todos los campos del FormData original
      for (let [key, value] of ofertaData.entries()) {
        // Manejar el campo visible de manera especial
        if (key === 'visible') {
          // Convertir a un valor booleano válido para PocketBase
          if (value === true || value === 'true' || value === '1' || value === 1) {
            formData.append('visible', true);
          } else {
            formData.append('visible', false);
          }
        } else if (key === 'dias_por_modelo') {
          // Asegurarse de que dias_por_modelo sea un JSON válido
          if (typeof value === 'object') {
            formData.append('dias_por_modelo', JSON.stringify(value));
          } else if (typeof value === 'string') {
            // Verificar si ya es un string JSON válido
            try {
              JSON.parse(value);
              formData.append('dias_por_modelo', value);
            } catch (e) {
              // Si no es un JSON válido, convertirlo a un objeto vacío
              formData.append('dias_por_modelo', '{}');
            }
          } else {
            formData.append('dias_por_modelo', '{}');
          }
        } else if (key === 'descripcion' && typeof value === 'string') {
          // Para el campo descripción, verificar si es un JSON válido
          try {
            // Intentar parsear para verificar si es JSON
            JSON.parse(value);
            // Si es JSON válido, no sanitizar
            formData.append(key, value);
          } catch (e) {
            // Si no es JSON válido, sanitizar como texto normal
            formData.append(key, sanitizeInput(value));
          }
        } else if (textFields.includes(key) && key !== 'descripcion' && typeof value === 'string') {
          // Sanitizar otros campos de texto para prevenir XSS
          formData.append(key, sanitizeInput(value));
        } else {
          // Para otros campos, copiar tal cual
          formData.append(key, value);
        }
      }

      // Asegurarse de que visible siempre tenga un valor
      if (!formData.has('visible')) {
        formData.append('visible', true); // Valor por defecto
      }

      // Validación básica de campos requeridos
      if (!formData.get('titulo')) {
        throw new Error('El título es obligatorio');
      }

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        const result = await db.collection('ofertas').create(formData, options);

        // Invalidar la caché para forzar una recarga en la próxima solicitud
        if (ofertasService._cache.data) {
          ofertasService._cache.timestamp = 0;
        }

        return result;
      }

      const result = await db.collection('ofertas').create(formData);

      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (ofertasService._cache.data) {
        ofertasService._cache.timestamp = 0;
      }

      return result;
    } catch (error) {
      const handledError = handleApiError(error, 'Error al crear oferta');
      throw handledError;
    }
  },

  // Actualizar una oferta existente
  update: async (id, ofertaData) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Crear una copia de los datos para manipularlos
      const formData = new FormData();

      // Lista de campos de texto que deben ser sanitizados
      const textFields = ['titulo', 'descripcion'];

      // Copiar todos los campos del FormData original
      for (let [key, value] of ofertaData.entries()) {
        // Manejar el campo visible de manera especial
        if (key === 'visible') {
          // Convertir a un valor booleano válido para PocketBase
          if (value === true || value === 'true' || value === '1' || value === 1) {
            formData.append('visible', true);
          } else {
            formData.append('visible', false);
          }
        } else if (key === 'dias_por_modelo') {
          // Asegurarse de que dias_por_modelo sea un JSON válido
          if (typeof value === 'object') {
            formData.append('dias_por_modelo', JSON.stringify(value));
          } else if (typeof value === 'string') {
            // Verificar si ya es un string JSON válido
            try {
              JSON.parse(value);
              formData.append('dias_por_modelo', value);
            } catch (e) {
              // Si no es un JSON válido, convertirlo a un objeto vacío
              formData.append('dias_por_modelo', '{}');
            }
          } else {
            formData.append('dias_por_modelo', '{}');
          }
        } else if (key === 'descripcion' && typeof value === 'string') {
          // Para el campo descripción, verificar si es un JSON válido
          try {
            // Intentar parsear para verificar si es JSON
            JSON.parse(value);
            // Si es JSON válido, no sanitizar
            formData.append(key, value);
          } catch (e) {
            // Si no es JSON válido, sanitizar como texto normal
            formData.append(key, sanitizeInput(value));
          }
        } else if (textFields.includes(key) && key !== 'descripcion' && typeof value === 'string') {
          // Sanitizar otros campos de texto para prevenir XSS
          formData.append(key, sanitizeInput(value));
        } else {
          // Para otros campos, copiar tal cual
          formData.append(key, value);
        }
      }

      // Asegurarse de que visible siempre tenga un valor
      if (!formData.has('visible')) {
        formData.append('visible', true); // Valor por defecto
      }

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        const result = await db.collection('ofertas').update(validatedId, formData, options);

        // Invalidar la caché para forzar una recarga en la próxima solicitud
        if (ofertasService._cache.data) {
          ofertasService._cache.timestamp = 0;
        }

        return result;
      }

      const result = await db.collection('ofertas').update(validatedId, formData);

      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (ofertasService._cache.data) {
        ofertasService._cache.timestamp = 0;
      }

      return result;
    } catch (error) {
      const handledError = handleApiError(error, `Error al actualizar oferta con ID ${id}`);
      throw handledError;
    }
  },

  // Eliminar una oferta
  delete: async (id) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        const result = await db.collection('ofertas').delete(validatedId, options);

        // Invalidar la caché para forzar una recarga en la próxima solicitud
        if (ofertasService._cache.data) {
          ofertasService._cache.timestamp = 0;
        }

        return result;
      }

      const result = await db.collection('ofertas').delete(validatedId);

      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (ofertasService._cache.data) {
        ofertasService._cache.timestamp = 0;
      }

      return result;
    } catch (error) {
      const handledError = handleApiError(error, `Error al eliminar oferta con ID ${id}`);
      throw handledError;
    }
  },

  // Cambiar visibilidad de una oferta
  toggleVisibility: async (id, visible) => {
    try {
      // Validar el ID antes de usarlo
      const validatedId = validateId(id);

      // Crear FormData con el nuevo valor de visible
      const formData = new FormData();
      formData.append('visible', visible);

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      if (csrfToken) {
        // Agregar el token como header en la solicitud
        const options = {
          headers: {
            'X-CSRF-Token': csrfToken
          }
        };
        const result = await db.collection('ofertas').update(validatedId, formData, options);

        // Invalidar la caché para forzar una recarga en la próxima solicitud
        if (ofertasService._cache.data) {
          ofertasService._cache.timestamp = 0;
        }

        return result;
      }

      const result = await db.collection('ofertas').update(validatedId, formData);

      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (ofertasService._cache.data) {
        ofertasService._cache.timestamp = 0;
        }

      return result;
    } catch (error) {
      const handledError = handleApiError(error, `Error al cambiar visibilidad de oferta con ID ${id}`);
      throw handledError;
    }
  }
};

export default ofertasService;
