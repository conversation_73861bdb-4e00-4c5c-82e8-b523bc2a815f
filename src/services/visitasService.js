import { modelosService } from './pocketbase';
import { independientesService } from './independientesService';
import logger from './logService';

/**
 * Servicio para gestionar el seguimiento de visitas a modelos e independientes
 */
const visitasService = {
  /**
   * Registra una visita a un modelo o independiente
   * @param {string} id - ID del modelo o independiente
   * @param {string} tipo - Tipo de modelo ('brujita' o 'independiente')
   * @returns {Promise<boolean>} - True si la visita fue registrada, false si no
   */
  registrarVisita: async (id, tipo) => {
    try {
      // Verificar si ya se visitó este modelo en los últimos 10 minutos
      if (visitasService.yaVisitado(id, tipo)) {
        logger.debug(`${tipo} ${id} ya fue visitado recientemente. No se registra nueva visita.`);
        return false;
      }

      // Obtener el modelo actual
      let modelo;
      let service;

      if (tipo === 'brujita') {
        modelo = await modelosService.getById(id);
        service = modelosService;
      } else if (tipo === 'independiente') {
        modelo = await independientesService.getById(id);
        service = independientesService;
      } else {
        throw new Error(`Tipo de modelo no válido: ${tipo}`);
      }

      // Incrementar el contador de visitas
      const visitasActuales = modelo.visitas || 0;
      const nuevasVisitas = visitasActuales + 1;

      // Actualizar el modelo con el nuevo contador
      // Crear un FormData para la actualización (compatible con ambos servicios)
      const formData = new FormData();
      formData.append('visitas', nuevasVisitas);

      await service.update(id, formData);

      // Registrar la visita en localStorage
      visitasService.guardarVisita(id, tipo);

      logger.debug(`Visita registrada para ${tipo} ${id}. Total: ${nuevasVisitas}`);
      return true;
    } catch (error) {
      logger.error(`Error al registrar visita para ${tipo} ${id}:`, error);
      return false;
    }
  },

  /**
   * Verifica si un modelo ya fue visitado en los últimos 10 minutos
   * @param {string} id - ID del modelo
   * @param {string} tipo - Tipo de modelo ('brujita' o 'independiente')
   * @returns {boolean} - True si ya fue visitado, false si no
   */
  yaVisitado: (id, tipo) => {
    try {
      const key = `visita_${tipo}_${id}`;
      const ultimaVisita = localStorage.getItem(key);

      if (!ultimaVisita) return false;

      // Verificar si han pasado menos de 10 minutos desde la última visita
      const tiempoTranscurrido = Date.now() - parseInt(ultimaVisita);
      const diezMinutosEnMs = 10 * 60 * 1000;

      return tiempoTranscurrido < diezMinutosEnMs;
    } catch (error) {
      logger.error('Error al verificar visita previa:', error);
      return false; // En caso de error, permitir el registro de la visita
    }
  },

  /**
   * Guarda el registro de una visita en localStorage
   * @param {string} id - ID del modelo
   * @param {string} tipo - Tipo de modelo ('brujita' o 'independiente')
   */
  guardarVisita: (id, tipo) => {
    try {
      const key = `visita_${tipo}_${id}`;
      localStorage.setItem(key, Date.now().toString());
    } catch (error) {
      logger.error('Error al guardar visita en localStorage:', error);
    }
  }
};

export default visitasService;
