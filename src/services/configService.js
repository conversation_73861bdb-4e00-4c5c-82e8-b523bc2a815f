import { db } from './pocketbase';
import { API_URL } from '../config';
import logger from './logService';
import { sanitizeInput, handleApiError } from '../utils/helpers';

/**
 * Servicio para gestionar la configuración en PocketBase
 */
export const configService = {
  // Caché para almacenar resultados de consultas recientes
  _cache: {
    data: null,
    timestamp: 0,
    loading: false,
    pendingRequests: []
  },

  // Tiempo de expiración de la caché en milisegundos (30 segundos)
  _cacheExpiry: 30000,

  // Obtener la configuración
  getConfig: async () => {
    // Verificar si hay una solicitud en curso
    if (configService._cache.loading) {
      // Si hay una solicitud en curso, devolver una promesa que se resolverá cuando termine
      return new Promise((resolve) => {
        configService._cache.pendingRequests.push(resolve);
      });
    }

    // Verificar si hay datos en caché y si son válidos
    const now = Date.now();
    if (configService._cache.data && (now - configService._cache.timestamp < configService._cacheExpiry)) {
      return configService._cache.data;
    }

    try {
      // Marcar como cargando para evitar solicitudes duplicadas
      configService._cache.loading = true;

      // Usar fetch con la URL centralizada
      const response = await fetch(`${API_URL}/api/collections/config/records`);

      if (!response.ok) {
        throw new Error(`Error HTTP: ${response.status}`);
      }

      const data = await response.json();

      // Verificar explícitamente si data.items existe
      if (!data || !data.items) {
        logger.warn('La respuesta de config no contiene la propiedad items o es nula');
        const result = { items: [], totalItems: 0, totalPages: 0 };

        // Actualizar caché
        configService._cache.data = result;
        configService._cache.timestamp = now;

        // Resolver todas las promesas pendientes
        if (configService._cache.pendingRequests.length > 0) {
          configService._cache.pendingRequests.forEach(resolve => resolve(result));
          configService._cache.pendingRequests = [];
        }

        return result;
      }

      // Actualizar caché
      configService._cache.data = data;
      configService._cache.timestamp = now;

      // Resolver todas las promesas pendientes
      if (configService._cache.pendingRequests.length > 0) {
        configService._cache.pendingRequests.forEach(resolve => resolve(data));
        configService._cache.pendingRequests = [];
      }

      return data;
    } catch (error) {
      logger.error('Error al obtener configuración:', error);
      // Devolver un array vacío para no romper la UI
      const result = { items: [], totalItems: 0, totalPages: 0, error };

      // Resolver todas las promesas pendientes con el error
      if (configService._cache.pendingRequests.length > 0) {
        configService._cache.pendingRequests.forEach(resolve => resolve(result));
        configService._cache.pendingRequests = [];
      }

      return result;
    } finally {
      // Marcar como no cargando
      configService._cache.loading = false;
    }
  },

  // Actualizar o crear la configuración
  updateConfig: async (id, configData) => {
    try {
      // Crear una copia de los datos para manipularlos
      const formData = new FormData();

      // Lista de campos de texto que deben ser sanitizados
      const textFields = ['telegram', 'wa', 'tiktok', 'twitter'];

      // Procesar cada campo
      for (const [key, value] of Object.entries(configData)) {
        // Solo procesar campos con valores no vacíos
        if (value !== undefined && value !== null && value.trim && value.trim() !== '') {
          // Sanitizar campos de texto
          if (textFields.includes(key)) {
            formData.append(key, sanitizeInput(value));
          }
          // Otros campos se agregan sin modificar
          else {
            formData.append(key, value);
          }
        }
      }

      // Agregar token CSRF si está disponible
      const csrfToken = sessionStorage.getItem('csrfToken');
      const options = csrfToken ? { headers: { 'X-CSRF-Token': csrfToken } } : {};

      let result;

      // Intentar actualizar si hay un ID, o crear si no existe o falla la actualización
      if (id) {
        try {
          result = await db.collection('config').update(id, formData, options);
        } catch (updateError) {
          // Si falla la actualización (por ejemplo, si el registro no existe), intentar crear
          logger.warn('No se pudo actualizar la configuración, intentando crear un nuevo registro:', updateError);
          result = await db.collection('config').create(formData, options);
        }
      } else {
        // Si no hay ID, crear un nuevo registro
        result = await db.collection('config').create(formData, options);
      }

      // Invalidar la caché para forzar una recarga en la próxima solicitud
      if (configService._cache.data) {
        configService._cache.timestamp = 0;
      }

      return result;
    } catch (error) {
      const handledError = handleApiError(error, 'Error al actualizar configuración');
      throw handledError;
    }
  },

  // Crear configuración inicial si no existe
  createInitialConfig: async () => {
    try {
      // Verificar si ya existe alguna configuración
      const config = await configService.getConfig();

      // Si ya hay configuración, no hacer nada
      if (config && config.items && config.items.length > 0) {
        return config.items[0];
      }

      // Crear configuración inicial
      const initialConfig = {
        telegram: 'https://t.me/BrujitasMx',
        wa: 'https://wa.me/+527205373745',
        tiktok: 'https://www.tiktok.com/@brujitas.sexys.mx',
        twitter: 'https://x.com/BrujitaSexMex'
      };

      // Crear el registro
      const formData = new FormData();
      for (const [key, value] of Object.entries(initialConfig)) {
        formData.append(key, value);
      }

      const csrfToken = sessionStorage.getItem('csrfToken');
      const options = csrfToken ? { headers: { 'X-CSRF-Token': csrfToken } } : {};

      const result = await db.collection('config').create(formData, options);

      // Invalidar la caché
      configService._cache.timestamp = 0;

      return result;
    } catch (error) {
      logger.error('Error al crear configuración inicial:', error);
      throw error;
    }
  }
};

export default configService;
