import { APP_CONFIG } from '../config';

/**
 * Servicio centralizado para manejo de logs
 * Permite controlar qué logs se muestran según el entorno
 */

// Niveles de log
const LOG_LEVELS = {
  NONE: 0,
  ERROR: 1,
  WARN: 2,
  INFO: 3,
  DEBUG: 4
};

// Obtener el nivel de log configurado
const currentLogLevel = APP_CONFIG.LOG_LEVEL || LOG_LEVELS.ERROR;

/**
 * Log de error - Solo se muestra si el nivel es ERROR o superior
 */
export const logError = (message, ...args) => {
  if (currentLogLevel >= LOG_LEVELS.ERROR) {
    console.error(`[ERROR] ${message}`, ...args);
  }
};

/**
 * Log de advertencia - Solo se muestra si el nivel es WARN o superior
 */
export const logWarn = (message, ...args) => {
  if (currentLogLevel >= LOG_LEVELS.WARN) {
    console.warn(`[WARN] ${message}`, ...args);
  }
};

/**
 * Log informativo - Solo se muestra si el nivel es INFO o superior
 */
export const logInfo = (message, ...args) => {
  if (currentLogLevel >= LOG_LEVELS.INFO) {
    console.info(`[INFO] ${message}`, ...args);
  }
};

/**
 * Log de depuración - Solo se muestra si el nivel es DEBUG
 */
export const logDebug = (message, ...args) => {
  if (currentLogLevel >= LOG_LEVELS.DEBUG) {
    console.debug(`[DEBUG] ${message}`, ...args);
  }
};

// Exportar el servicio completo
export default {
  error: logError,
  warn: logWarn,
  info: logInfo,
  debug: logDebug,
  levels: LOG_LEVELS
};
